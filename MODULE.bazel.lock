{"lockFileVersion": 18, "registryFileHashes": {"https://bcr.bazel.build/bazel_registry.json": "8a28e4aff06ee60aed2a8c281907fb8bcbf3b753c91fb5a5c57da3215d5b3497", "https://bcr.bazel.build/modules/abseil-cpp/20210324.2/MODULE.bazel": "7cd0312e064fde87c8d1cd79ba06c876bd23630c83466e9500321be55c96ace2", "https://bcr.bazel.build/modules/abseil-cpp/20211102.0/MODULE.bazel": "70390338f7a5106231d20620712f7cccb659cd0e9d073d1991c038eb9fc57589", "https://bcr.bazel.build/modules/abseil-cpp/20220623.1/MODULE.bazel": "73ae41b6818d423a11fd79d95aedef1258f304448193d4db4ff90e5e7a0f076c", "https://bcr.bazel.build/modules/abseil-cpp/20230125.1/MODULE.bazel": "89047429cb0207707b2dface14ba7f8df85273d484c2572755be4bab7ce9c3a0", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0.bcr.1/MODULE.bazel": "1c8cec495288dccd14fdae6e3f95f772c1c91857047a098fad772034264cc8cb", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0/MODULE.bazel": "d253ae36a8bd9ee3c5955384096ccb6baf16a1b1e93e858370da0a3b94f77c16", "https://bcr.bazel.build/modules/abseil-cpp/20230802.1/MODULE.bazel": "fa92e2eb41a04df73cdabeec37107316f7e5272650f81d6cc096418fe647b915", "https://bcr.bazel.build/modules/abseil-cpp/20240116.0/MODULE.bazel": "98dc378d64c12a4e4741ad3362f87fb737ee6a0886b2d90c3cdbb4d93ea3e0bf", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/MODULE.bazel": "37bcdb4440fbb61df6a1c296ae01b327f19e9bb521f9b8e26ec854b6f97309ed", "https://bcr.bazel.build/modules/abseil-cpp/20240116.2/MODULE.bazel": "73939767a4686cd9a520d16af5ab440071ed75cec1a876bf2fcfaf1f71987a16", "https://bcr.bazel.build/modules/abseil-cpp/20240722.0/MODULE.bazel": "88668a07647adbdc14cb3a7cd116fb23c9dda37a90a1681590b6c9d8339a5b84", "https://bcr.bazel.build/modules/abseil-cpp/20250127.0/MODULE.bazel": "d1086e248cda6576862b4b3fe9ad76a214e08c189af5b42557a6e1888812c5d5", "https://bcr.bazel.build/modules/abseil-cpp/20250127.0/source.json": "1b996859f840d8efc7c720efc61dcf2a84b1261cb3974cbbe9b6666ebf567775", "https://bcr.bazel.build/modules/apple_rules_lint/0.4.0/MODULE.bazel": "c59831c3a5389430516203777816527f257329a5da363994e1d62b9ae6729f71", "https://bcr.bazel.build/modules/apple_rules_lint/0.4.0/source.json": "105883202602181f43f109372e1b9ea19e89bbe3bce4bc1fe9bb0baa51eb61ae", "https://bcr.bazel.build/modules/apple_support/1.11.1/MODULE.bazel": "1843d7cd8a58369a444fc6000e7304425fba600ff641592161d9f15b179fb896", "https://bcr.bazel.build/modules/apple_support/1.15.1/MODULE.bazel": "a0556fefca0b1bb2de8567b8827518f94db6a6e7e7d632b4c48dc5f865bc7c85", "https://bcr.bazel.build/modules/apple_support/1.15.1/source.json": "517f2b77430084c541bc9be2db63fdcbb7102938c5f64c17ee60ffda2e5cf07b", "https://bcr.bazel.build/modules/bazel_features/1.1.0/MODULE.bazel": "cfd42ff3b815a5f39554d97182657f8c4b9719568eb7fded2b9135f084bf760b", "https://bcr.bazel.build/modules/bazel_features/1.1.1/MODULE.bazel": "27b8c79ef57efe08efccbd9dd6ef70d61b4798320b8d3c134fd571f78963dbcd", "https://bcr.bazel.build/modules/bazel_features/1.11.0/MODULE.bazel": "f9382337dd5a474c3b7d334c2f83e50b6eaedc284253334cf823044a26de03e8", "https://bcr.bazel.build/modules/bazel_features/1.15.0/MODULE.bazel": "d38ff6e517149dc509406aca0db3ad1efdd890a85e049585b7234d04238e2a4d", "https://bcr.bazel.build/modules/bazel_features/1.17.0/MODULE.bazel": "039de32d21b816b47bd42c778e0454217e9c9caac4a3cf8e15c7231ee3ddee4d", "https://bcr.bazel.build/modules/bazel_features/1.18.0/MODULE.bazel": "1be0ae2557ab3a72a57aeb31b29be347bcdc5d2b1eb1e70f39e3851a7e97041a", "https://bcr.bazel.build/modules/bazel_features/1.19.0/MODULE.bazel": "59adcdf28230d220f0067b1f435b8537dd033bfff8db21335ef9217919c7fb58", "https://bcr.bazel.build/modules/bazel_features/1.21.0/MODULE.bazel": "675642261665d8eea09989aa3b8afb5c37627f1be178382c320d1b46afba5e3b", "https://bcr.bazel.build/modules/bazel_features/1.23.0/MODULE.bazel": "fd1ac84bc4e97a5a0816b7fd7d4d4f6d837b0047cf4cbd81652d616af3a6591a", "https://bcr.bazel.build/modules/bazel_features/1.3.0/MODULE.bazel": "cdcafe83ec318cda34e02948e81d790aab8df7a929cec6f6969f13a489ccecd9", "https://bcr.bazel.build/modules/bazel_features/1.30.0/MODULE.bazel": "a14b62d05969a293b80257e72e597c2da7f717e1e69fa8b339703ed6731bec87", "https://bcr.bazel.build/modules/bazel_features/1.30.0/source.json": "b07e17f067fe4f69f90b03b36ef1e08fe0d1f3cac254c1241a1818773e3423bc", "https://bcr.bazel.build/modules/bazel_features/1.4.1/MODULE.bazel": "e45b6bb2350aff3e442ae1111c555e27eac1d915e77775f6fdc4b351b758b5d7", "https://bcr.bazel.build/modules/bazel_features/1.9.1/MODULE.bazel": "8f679097876a9b609ad1f60249c49d68bfab783dd9be012faf9d82547b14815a", "https://bcr.bazel.build/modules/bazel_skylib/1.0.3/MODULE.bazel": "bcb0fd896384802d1ad283b4e4eb4d718eebd8cb820b0a2c3a347fb971afd9d8", "https://bcr.bazel.build/modules/bazel_skylib/1.1.1/MODULE.bazel": "1add3e7d93ff2e6998f9e118022c84d163917d912f5afafb3058e3d2f1545b5e", "https://bcr.bazel.build/modules/bazel_skylib/1.2.0/MODULE.bazel": "44fe84260e454ed94ad326352a698422dbe372b21a1ac9f3eab76eb531223686", "https://bcr.bazel.build/modules/bazel_skylib/1.2.1/MODULE.bazel": "f35baf9da0efe45fa3da1696ae906eea3d615ad41e2e3def4aeb4e8bc0ef9a7a", "https://bcr.bazel.build/modules/bazel_skylib/1.3.0/MODULE.bazel": "20228b92868bf5cfc41bda7afc8a8ba2a543201851de39d990ec957b513579c5", "https://bcr.bazel.build/modules/bazel_skylib/1.4.1/MODULE.bazel": "a0dcb779424be33100dcae821e9e27e4f2901d9dfd5333efe5ac6a8d7ab75e1d", "https://bcr.bazel.build/modules/bazel_skylib/1.4.2/MODULE.bazel": "3bd40978e7a1fac911d5989e6b09d8f64921865a45822d8b09e815eaa726a651", "https://bcr.bazel.build/modules/bazel_skylib/1.5.0/MODULE.bazel": "32880f5e2945ce6a03d1fbd588e9198c0a959bb42297b2cfaf1685b7bc32e138", "https://bcr.bazel.build/modules/bazel_skylib/1.6.1/MODULE.bazel": "8fdee2dbaace6c252131c00e1de4b165dc65af02ea278476187765e1a617b917", "https://bcr.bazel.build/modules/bazel_skylib/1.7.0/MODULE.bazel": "0db596f4563de7938de764cc8deeabec291f55e8ec15299718b93c4423e9796d", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/MODULE.bazel": "3120d80c5861aa616222ec015332e5f8d3171e062e3e804a2a0253e1be26e59b", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/source.json": "f121b43eeefc7c29efbd51b83d08631e2347297c95aac9764a701f2a6a2bb953", "https://bcr.bazel.build/modules/boringssl/0.0.0-20211025-d4f1ab9/MODULE.bazel": "6ee6353f8b1a701fe2178e1d925034294971350b6d3ac37e67e5a7d463267834", "https://bcr.bazel.build/modules/boringssl/0.0.0-20230215-5c22014/MODULE.bazel": "4b03dc0d04375fa0271174badcd202ed249870c8e895b26664fd7298abea7282", "https://bcr.bazel.build/modules/boringssl/0.0.0-20240530-2db0eb3/MODULE.bazel": "d0405b762c5e87cd445b7015f2b8da5400ef9a8dbca0bfefa6c1cea79d528a97", "https://bcr.bazel.build/modules/boringssl/0.20240913.0/MODULE.bazel": "fcaa7503a5213290831a91ed1eb538551cf11ac0bc3a6ad92d0fef92c5bd25fb", "https://bcr.bazel.build/modules/boringssl/0.20241024.0/MODULE.bazel": "b540cff73d948cb79cb0bc108d7cef391d2098a25adabfda5043e4ef548dbc87", "https://bcr.bazel.build/modules/boringssl/0.20241024.0/source.json": "d843092e682b84188c043ac742965d7f96e04c846c7e338187e03238674909a9", "https://bcr.bazel.build/modules/buildozer/7.1.2/MODULE.bazel": "2e8dd40ede9c454042645fd8d8d0cd1527966aa5c919de86661e62953cd73d84", "https://bcr.bazel.build/modules/buildozer/7.1.2/source.json": "c9028a501d2db85793a6996205c8de120944f50a0d570438fcae0457a5f9d1f8", "https://bcr.bazel.build/modules/c-ares/1.15.0/MODULE.bazel": "ba0a78360fdc83f02f437a9e7df0532ad1fbaa59b722f6e715c11effebaa0166", "https://bcr.bazel.build/modules/c-ares/1.15.0/source.json": "5e3ed991616c5ec4cc09b0893b29a19232de4a1830eb78c567121bfea87453f7", "https://bcr.bazel.build/modules/cel-spec/0.15.0/MODULE.bazel": "e1eed53d233acbdcf024b4b0bc1528116d92c29713251b5154078ab1348cb600", "https://bcr.bazel.build/modules/cel-spec/0.15.0/source.json": "ab7dccdf21ea2261c0f809b5a5221a4d7f8b580309f285fdf1444baaca75d44a", "https://bcr.bazel.build/modules/civetweb/1.16/MODULE.bazel": "46a38f9daeb57392e3827fce7d40926be0c802bd23cdd6bfd3a96c804de42fae", "https://bcr.bazel.build/modules/civetweb/1.16/source.json": "ba8b9585adb8355cb51b999d57172fd05e7a762c56b8d4bac6db42c99de3beb7", "https://bcr.bazel.build/modules/contrib_rules_jvm/0.29.0/MODULE.bazel": "3e8ff80d17952340c4932a3396738ef714967fd40d0673fa7640a0d86028230a", "https://bcr.bazel.build/modules/contrib_rules_jvm/0.29.0/source.json": "12d2e5ea4a3f34753e93dd2f5e3bd4a5fca255e0b8dfa09d0b61a15a00ae6f2f", "https://bcr.bazel.build/modules/curl/8.4.0/MODULE.bazel": "0bc250aa1cb69590049383df7a9537c809591fcf876c620f5f097c58fdc9bc10", "https://bcr.bazel.build/modules/curl/8.7.1/MODULE.bazel": "088221c35a2939c555e6e47cb31a81c15f8b59f4daa8009b1e9271a502d33485", "https://bcr.bazel.build/modules/curl/8.7.1/source.json": "bf9890e809717445b10a3ddc323b6d25c46631589c693a232df8310a25964484", "https://bcr.bazel.build/modules/cython/3.0.11-1/MODULE.bazel": "868b3f5c956c3657420d2302004c6bb92606bfa47e314bab7f2ba0630c7c966c", "https://bcr.bazel.build/modules/cython/3.0.11-1/source.json": "da318be900b8ca9c3d1018839d3bebc5a8e1645620d0848fa2c696d4ecf7c296", "https://bcr.bazel.build/modules/envoy_api/0.0.0-20241214-918efc9/MODULE.bazel": "24e05f6f52f37be63a795192848555a2c8c855e7814dbc1ed419fb04a7005464", "https://bcr.bazel.build/modules/envoy_api/0.0.0-20241214-918efc9/source.json": "212043ab69d87f7a04aa4f627f725b540cff5e145a3a31a9403d8b6ec2e920c9", "https://bcr.bazel.build/modules/gazelle/0.27.0/MODULE.bazel": "3446abd608295de6d90b4a8a118ed64a9ce11dcb3dda2dc3290a22056bd20996", "https://bcr.bazel.build/modules/gazelle/0.30.0/MODULE.bazel": "f888a1effe338491f35f0e0e85003b47bb9d8295ccba73c37e07702d8d31c65b", "https://bcr.bazel.build/modules/gazelle/0.32.0/MODULE.bazel": "b499f58a5d0d3537f3cf5b76d8ada18242f64ec474d8391247438bf04f58c7b8", "https://bcr.bazel.build/modules/gazelle/0.33.0/MODULE.bazel": "a13a0f279b462b784fb8dd52a4074526c4a2afe70e114c7d09066097a46b3350", "https://bcr.bazel.build/modules/gazelle/0.34.0/MODULE.bazel": "abdd8ce4d70978933209db92e436deb3a8b737859e9354fb5fd11fb5c2004c8a", "https://bcr.bazel.build/modules/gazelle/0.36.0/MODULE.bazel": "e375d5d6e9a6ca59b0cb38b0540bc9a05b6aa926d322f2de268ad267a2ee74c0", "https://bcr.bazel.build/modules/gazelle/0.42.0/MODULE.bazel": "fa140a7c019f3a22779ba7c6132ffff9d2d10a51dba2f3304dee61523d11fef4", "https://bcr.bazel.build/modules/gazelle/0.45.0/MODULE.bazel": "ecd19ebe9f8e024e1ccffb6d997cc893a974bcc581f1ae08f386bdd448b10687", "https://bcr.bazel.build/modules/gazelle/0.45.0/source.json": "111d182facc5f5e80f0b823d5f077b74128f40c3fd2eccc89a06f34191bd3392", "https://bcr.bazel.build/modules/google_benchmark/1.8.2/MODULE.bazel": "a70cf1bba851000ba93b58ae2f6d76490a9feb74192e57ab8e8ff13c34ec50cb", "https://bcr.bazel.build/modules/google_benchmark/1.8.4/MODULE.bazel": "c6d54a11dcf64ee63545f42561eda3fd94c1b5f5ebe1357011de63ae33739d5e", "https://bcr.bazel.build/modules/google_benchmark/1.8.5/MODULE.bazel": "9ba9b31b984022828a950e3300410977eda2e35df35584c6b0b2d0c2e52766b7", "https://bcr.bazel.build/modules/google_benchmark/1.8.5/source.json": "2c9c685f9b496f125b9e3a9c696c549d1ed2f33b75830a2fb6ac94fab23c0398", "https://bcr.bazel.build/modules/googleapis/0.0.0-20240326-1c8d509c5/MODULE.bazel": "a4b7e46393c1cdcc5a00e6f85524467c48c565256b22b5fae20f84ab4a999a68", "https://bcr.bazel.build/modules/googleapis/0.0.0-20240819-fe8ba054a/MODULE.bazel": "117b7c7be7327ed5d6c482274533f2dbd78631313f607094d4625c28203cacdf", "https://bcr.bazel.build/modules/googleapis/0.0.0-20240819-fe8ba054a/source.json": "b31fc7eb283a83f71d2e5bfc3d1c562d2994198fa1278409fbe8caec3afc1d3e", "https://bcr.bazel.build/modules/googletest/1.11.0/MODULE.bazel": "3a83f095183f66345ca86aa13c58b59f9f94a2f81999c093d4eeaa2d262d12f4", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/MODULE.bazel": "22c31a561553727960057361aa33bf20fb2e98584bc4fec007906e27053f80c6", "https://bcr.bazel.build/modules/googletest/1.14.0/MODULE.bazel": "cfbcbf3e6eac06ef9d85900f64424708cc08687d1b527f0ef65aa7517af8118f", "https://bcr.bazel.build/modules/googletest/1.15.2/MODULE.bazel": "6de1edc1d26cafb0ea1a6ab3f4d4192d91a312fd2d360b63adaa213cd00b2108", "https://bcr.bazel.build/modules/googletest/1.15.2/source.json": "dbdda654dcb3a0d7a8bc5d0ac5fc7e150b58c2a986025ae5bc634bb2cb61f470", "https://bcr.bazel.build/modules/grpc-java/1.62.2/MODULE.bazel": "99b8771e8c7cacb130170fed2a10c9e8fed26334a93e73b42d2953250885a158", "https://bcr.bazel.build/modules/grpc-java/1.66.0/MODULE.bazel": "86ff26209fac846adb89db11f3714b3dc0090fb2fb81575673cc74880cda4e7e", "https://bcr.bazel.build/modules/grpc-java/1.69.0/MODULE.bazel": "53887af6a00b3b406d70175d3d07e84ea9362016ff55ea90b9185f0227bfaf98", "https://bcr.bazel.build/modules/grpc-java/1.71.0/MODULE.bazel": "a0ca84909a119ce24f5966978fdb4ab857bcddb5866b34af2e693fc6648db228", "https://bcr.bazel.build/modules/grpc-java/1.71.0/source.json": "aa8c2bf0f67e499c450a2a14a05a588eb852ffb82b9a6864274655271995795f", "https://bcr.bazel.build/modules/grpc-proto/0.0.0-20240627-ec30f58/MODULE.bazel": "88de79051e668a04726e9ea94a481ec6f1692086735fd6f488ab908b3b909238", "https://bcr.bazel.build/modules/grpc-proto/0.0.0-20240627-ec30f58/source.json": "5035d379c61042930244ab59e750106d893ec440add92ec0df6a0098ca7f131d", "https://bcr.bazel.build/modules/grpc/1.41.0/MODULE.bazel": "5bcbfc2b274dabea628f0649dc50c90cf36543b1cfc31624832538644ad1aae8", "https://bcr.bazel.build/modules/grpc/1.56.3.bcr.1/MODULE.bazel": "cd5b1eb276b806ec5ab85032921f24acc51735a69ace781be586880af20ab33f", "https://bcr.bazel.build/modules/grpc/1.62.1/MODULE.bazel": "2998211594b8a79a6b459c4e797cfa19f0fb8b3be3149760ec7b8c99abfd426f", "https://bcr.bazel.build/modules/grpc/1.66.0.bcr.2/MODULE.bazel": "0fa2b0fd028ce354febf0fe90f1ed8fecfbfc33118cddd95ac0418cc283333a0", "https://bcr.bazel.build/modules/grpc/1.66.0.bcr.3/MODULE.bazel": "f6047e89faf488f5e3e65cb2594c6f5e86992abec7487163ff6b623526e543b0", "https://bcr.bazel.build/modules/grpc/1.69.0/MODULE.bazel": "4e26e05c9e1ef291ccbc96aad8e457b1b8abedbc141623831629da2f8168eef6", "https://bcr.bazel.build/modules/grpc/1.69.0/source.json": "82e5cd0eeed569909ff42fd6946b813c8b69fc71a6b075ccebef224937e19215", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/MODULE.bazel": "31271aedc59e815656f5736f282bb7509a97c7ecb43e927ac1a37966e0578075", "https://bcr.bazel.build/modules/jsoncpp/1.9.6/MODULE.bazel": "2f8d20d3b7d54143213c4dfc3d98225c42de7d666011528dc8fe91591e2e17b0", "https://bcr.bazel.build/modules/jsoncpp/1.9.6/source.json": "a04756d367a2126c3541682864ecec52f92cdee80a35735a3cb249ce015ca000", "https://bcr.bazel.build/modules/libpfm/4.11.0/MODULE.bazel": "45061ff025b301940f1e30d2c16bea596c25b176c8b6b3087e92615adbd52902", "https://bcr.bazel.build/modules/libpfm/4.11.0/source.json": "caaffb3ac2b59b8aac456917a4ecf3167d40478ee79f15ab7a877ec9273937c9", "https://bcr.bazel.build/modules/mbedtls/3.6.0/MODULE.bazel": "8e380e4698107c5f8766264d4df92e36766248447858db28187151d884995a09", "https://bcr.bazel.build/modules/mbedtls/3.6.0/source.json": "1dbe7eb5258050afcc3806b9d43050f71c6f539ce0175535c670df606790b30c", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/MODULE.bazel": "87023db2f55fc3a9949c7b08dc711fae4d4be339a80a99d04453c4bb3998eefc", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/source.json": "296c63a90c6813e53b3812d24245711981fc7e563d98fe15625f55181494488a", "https://bcr.bazel.build/modules/nlohmann_json/3.6.1/MODULE.bazel": "6f7b417dcc794d9add9e556673ad25cb3ba835224290f4f848f8e2db1e1fca74", "https://bcr.bazel.build/modules/opencensus-cpp/0.0.0-20230502-50eb5de/MODULE.bazel": "02201d2921dadb4ec90c4980eca4b2a02904eddcf6fa02f3da7594fb7b0d821c", "https://bcr.bazel.build/modules/opencensus-cpp/0.0.0-20230502-50eb5de/source.json": "f50efc07822f5425bd1d3e40e977484f9c0142463052717d40ec85cd6744243e", "https://bcr.bazel.build/modules/opencensus-proto/0.4.1/MODULE.bazel": "4a2e8b4d0b544002502474d611a5a183aa282251e14f6a01afe841c0c1b10372", "https://bcr.bazel.build/modules/opencensus-proto/0.4.1/source.json": "a7d956700a85b833c43fc61455c0e111ab75bab40768ed17a206ee18a2bbe38f", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.14.2/MODULE.bazel": "089a5613c2a159c7dfde098dabfc61e966889c7d6a81a98422a84c51535ed17d", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.16.0/MODULE.bazel": "b7379a140f538cea3f749179a2d481ed81942cc6f7b05a6113723eb34ac3b3e7", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.16.0/source.json": "da0cf667713b1e48d7f8912b100b4e0a8284c8a95717af5eb8c830d699e61cf5", "https://bcr.bazel.build/modules/opentelemetry-proto/1.1.0/MODULE.bazel": "a49f406e99bf05ab43ed4f5b3322fbd33adfd484b6546948929d1316299b68bf", "https://bcr.bazel.build/modules/opentelemetry-proto/1.3.1/MODULE.bazel": "0141a50e989576ee064c11ce8dd5ec89993525bd9f9a09c5618e4dacc8df9352", "https://bcr.bazel.build/modules/opentelemetry-proto/1.4.0.bcr.1/MODULE.bazel": "5ceaf25e11170d22eded4c8032728b4a3f273765fccda32f9e94f463755c4167", "https://bcr.bazel.build/modules/opentelemetry-proto/1.4.0.bcr.1/source.json": "fb9e01517460cfad8bafab082f2e1508d3cc2b7ed700cff19f3c7c84b146e5eb", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/MODULE.bazel": "b3925269f63561b8b880ae7cf62ccf81f6ece55b62cd791eda9925147ae116ec", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/source.json": "da1cb1add160f5e5074b7272e9db6fd8f1b3336c15032cd0a653af9d2f484aed", "https://bcr.bazel.build/modules/package_metadata/0.0.5/MODULE.bazel": "ef4f9439e3270fdd6b9fd4dbc3d2f29d13888e44c529a1b243f7a31dfbc2e8e4", "https://bcr.bazel.build/modules/package_metadata/0.0.5/source.json": "2326db2f6592578177751c3e1f74786b79382cd6008834c9d01ec865b9126a85", "https://bcr.bazel.build/modules/platforms/0.0.10/MODULE.bazel": "8cb8efaf200bdeb2150d93e162c40f388529a25852b332cec879373771e48ed5", "https://bcr.bazel.build/modules/platforms/0.0.11/MODULE.bazel": "0daefc49732e227caa8bfa834d65dc52e8cc18a2faf80df25e8caea151a9413f", "https://bcr.bazel.build/modules/platforms/0.0.4/MODULE.bazel": "9b328e31ee156f53f3c416a64f8491f7eb731742655a47c9eec4703a71644aee", "https://bcr.bazel.build/modules/platforms/0.0.5/MODULE.bazel": "5733b54ea419d5eaf7997054bb55f6a1d0b5ff8aedf0176fef9eea44f3acda37", "https://bcr.bazel.build/modules/platforms/0.0.6/MODULE.bazel": "ad6eeef431dc52aefd2d77ed20a4b353f8ebf0f4ecdd26a807d2da5aa8cd0615", "https://bcr.bazel.build/modules/platforms/0.0.7/MODULE.bazel": "72fd4a0ede9ee5c021f6a8dd92b503e089f46c227ba2813ff183b71616034814", "https://bcr.bazel.build/modules/platforms/0.0.8/MODULE.bazel": "9f142c03e348f6d263719f5074b21ef3adf0b139ee4c5133e2aa35664da9eb2d", "https://bcr.bazel.build/modules/platforms/0.0.9/MODULE.bazel": "4a87a60c927b56ddd67db50c89acaa62f4ce2a1d2149ccb63ffd871d5ce29ebc", "https://bcr.bazel.build/modules/platforms/1.0.0/MODULE.bazel": "f05feb42b48f1b3c225e4ccf351f367be0371411a803198ec34a389fb22aa580", "https://bcr.bazel.build/modules/platforms/1.0.0/source.json": "f4ff1fd412e0246fd38c82328eb209130ead81d62dcd5a9e40910f867f733d96", "https://bcr.bazel.build/modules/prometheus-cpp/1.2.4/MODULE.bazel": "0fbe5dcff66311947a3f6b86ebc6a6d9328e31a28413ca864debc4a043f371e5", "https://bcr.bazel.build/modules/prometheus-cpp/1.3.0/MODULE.bazel": "ce82e086bbc0b60267e970f6a54b2ca6d0f22d3eb6633e00e2cc2899c700f3d8", "https://bcr.bazel.build/modules/prometheus-cpp/1.3.0/source.json": "8cb66b4e535afc718e9d104a3db96ccb71a42ee816a100e50fd0d5ac843c0606", "https://bcr.bazel.build/modules/protobuf/21.7/MODULE.bazel": "a5a29bb89544f9b97edce05642fac225a808b5b7be74038ea3640fae2f8e66a7", "https://bcr.bazel.build/modules/protobuf/23.1/MODULE.bazel": "88b393b3eb4101d18129e5db51847cd40a5517a53e81216144a8c32dfeeca52a", "https://bcr.bazel.build/modules/protobuf/24.4/MODULE.bazel": "7bc7ce5f2abf36b3b7b7c8218d3acdebb9426aeb35c2257c96445756f970eb12", "https://bcr.bazel.build/modules/protobuf/26.0.bcr.1/MODULE.bazel": "8f04d38c2da40a3715ff6bdce4d32c5981e6432557571482d43a62c31a24c2cf", "https://bcr.bazel.build/modules/protobuf/26.0.bcr.2/MODULE.bazel": "62e0b84ca727bdeb55a6fe1ef180e6b191bbe548a58305ea1426c158067be534", "https://bcr.bazel.build/modules/protobuf/26.0/MODULE.bazel": "8402da964092af40097f4a205eec2a33fd4a7748dc43632b7d1629bfd9a2b856", "https://bcr.bazel.build/modules/protobuf/27.0-rc2/MODULE.bazel": "b2b0dbafd57b6bec0ca9b251da02e628c357dab53a097570aa7d79d020f107cf", "https://bcr.bazel.build/modules/protobuf/27.0/MODULE.bazel": "7873b60be88844a0a1d8f80b9d5d20cfbd8495a689b8763e76c6372998d3f64c", "https://bcr.bazel.build/modules/protobuf/27.1/MODULE.bazel": "703a7b614728bb06647f965264967a8ef1c39e09e8f167b3ca0bb1fd80449c0d", "https://bcr.bazel.build/modules/protobuf/29.0-rc2.bcr.1/MODULE.bazel": "52f4126f63a2f0bbf36b99c2a87648f08467a4eaf92ba726bc7d6a500bbf770c", "https://bcr.bazel.build/modules/protobuf/29.0-rc2/MODULE.bazel": "6241d35983510143049943fc0d57937937122baf1b287862f9dc8590fc4c37df", "https://bcr.bazel.build/modules/protobuf/29.0-rc3/MODULE.bazel": "33c2dfa286578573afc55a7acaea3cada4122b9631007c594bf0729f41c8de92", "https://bcr.bazel.build/modules/protobuf/29.0/MODULE.bazel": "319dc8bf4c679ff87e71b1ccfb5a6e90a6dbc4693501d471f48662ac46d04e4e", "https://bcr.bazel.build/modules/protobuf/29.1/MODULE.bazel": "557c3457560ff49e122ed76c0bc3397a64af9574691cb8201b4e46d4ab2ecb95", "https://bcr.bazel.build/modules/protobuf/3.19.0/MODULE.bazel": "6b5fbb433f760a99a22b18b6850ed5784ef0e9928a72668b66e4d7ccd47db9b0", "https://bcr.bazel.build/modules/protobuf/3.19.2/MODULE.bazel": "532ffe5f2186b69fdde039efe6df13ba726ff338c6bc82275ad433013fa10573", "https://bcr.bazel.build/modules/protobuf/3.19.6/MODULE.bazel": "9233edc5e1f2ee276a60de3eaa47ac4132302ef9643238f23128fea53ea12858", "https://bcr.bazel.build/modules/protobuf/31.1/MODULE.bazel": "379a389bb330b7b8c1cdf331cc90bf3e13de5614799b3b52cdb7c6f389f6b38e", "https://bcr.bazel.build/modules/protobuf/31.1/source.json": "25af5d0219da0c0fc4d1191a24ce438e6ca7f49d2e1a94f354efeba6ef10426f", "https://bcr.bazel.build/modules/protoc-gen-validate/1.0.4.bcr.2/MODULE.bazel": "c4bd2c850211ff5b7dadf9d2d0496c1c922fdedc303c775b01dfd3b3efc907ed", "https://bcr.bazel.build/modules/protoc-gen-validate/1.0.4.bcr.2/source.json": "4cc97f70b521890798058600a927ce4b0def8ee84ff2a5aa632aabcb4234aa0b", "https://bcr.bazel.build/modules/protoc-gen-validate/1.0.4/MODULE.bazel": "b8913c154b16177990f6126d2d2477d187f9ddc568e95ee3e2d50fc65d2c494a", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/MODULE.bazel": "88af1c246226d87e65be78ed49ecd1e6f5e98648558c14ce99176da041dc378e", "https://bcr.bazel.build/modules/pybind11_bazel/2.12.0/MODULE.bazel": "e6f4c20442eaa7c90d7190d8dc539d0ab422f95c65a57cc59562170c58ae3d34", "https://bcr.bazel.build/modules/pybind11_bazel/2.12.0/source.json": "6900fdc8a9e95866b8c0d4ad4aba4d4236317b5c1cd04c502df3f0d33afed680", "https://bcr.bazel.build/modules/rapidjson/1.1.0.bcr.20241007/MODULE.bazel": "82fbcb2e42f9e0040e76ccc74c06c3e46dfd33c64ca359293f8b84df0e6dff4c", "https://bcr.bazel.build/modules/rapidjson/1.1.0.bcr.20241007/source.json": "5c42389ad0e21fc06b95ad7c0b730008271624a2fa3292e0eab5f30e15adeee3", "https://bcr.bazel.build/modules/re2/2021-09-01/MODULE.bazel": "bcb6b96f3b071e6fe2d8bed9cc8ada137a105f9d2c5912e91d27528b3d123833", "https://bcr.bazel.build/modules/re2/2023-09-01/MODULE.bazel": "cb3d511531b16cfc78a225a9e2136007a48cf8a677e4264baeab57fe78a80206", "https://bcr.bazel.build/modules/re2/2024-05-01/MODULE.bazel": "55a3f059538f381107824e7d00df5df6d061ba1fb80e874e4909c0f0549e8f3e", "https://bcr.bazel.build/modules/re2/2024-07-02.bcr.1/MODULE.bazel": "b4963dda9b31080be1905ef085ecd7dd6cd47c05c79b9cdf83ade83ab2ab271a", "https://bcr.bazel.build/modules/re2/2024-07-02.bcr.1/source.json": "2ff292be6ef3340325ce8a045ecc326e92cbfab47c7cbab4bd85d28971b97ac4", "https://bcr.bazel.build/modules/re2/2024-07-02/MODULE.bazel": "0eadc4395959969297cbcf31a249ff457f2f1d456228c67719480205aa306daa", "https://bcr.bazel.build/modules/rules_android/0.1.1/MODULE.bazel": "48809ab0091b07ad0182defb787c4c5328bd3a278938415c00a7b69b50c4d3a8", "https://bcr.bazel.build/modules/rules_android/0.1.1/source.json": "e6986b41626ee10bdc864937ffb6d6bf275bb5b9c65120e6137d56e6331f089e", "https://bcr.bazel.build/modules/rules_apple/3.16.0/MODULE.bazel": "0d1caf0b8375942ce98ea944be754a18874041e4e0459401d925577624d3a54a", "https://bcr.bazel.build/modules/rules_apple/3.16.0/source.json": "d8b5fe461272018cc07cfafce11fe369c7525330804c37eec5a82f84cd475366", "https://bcr.bazel.build/modules/rules_apple/3.5.1/MODULE.bazel": "3d1bbf65ad3692003d36d8a29eff54d4e5c1c5f4bfb60f79e28646a924d9101c", "https://bcr.bazel.build/modules/rules_cc/0.0.1/MODULE.bazel": "cb2aa0747f84c6c3a78dad4e2049c154f08ab9d166b1273835a8174940365647", "https://bcr.bazel.build/modules/rules_cc/0.0.10/MODULE.bazel": "ec1705118f7eaedd6e118508d3d26deba2a4e76476ada7e0e3965211be012002", "https://bcr.bazel.build/modules/rules_cc/0.0.13/MODULE.bazel": "0e8529ed7b323dad0775ff924d2ae5af7640b23553dfcd4d34344c7e7a867191", "https://bcr.bazel.build/modules/rules_cc/0.0.14/MODULE.bazel": "5e343a3aac88b8d7af3b1b6d2093b55c347b8eefc2e7d1442f7a02dc8fea48ac", "https://bcr.bazel.build/modules/rules_cc/0.0.15/MODULE.bazel": "6704c35f7b4a72502ee81f61bf88706b54f06b3cbe5558ac17e2e14666cd5dcc", "https://bcr.bazel.build/modules/rules_cc/0.0.16/MODULE.bazel": "7661303b8fc1b4d7f532e54e9d6565771fea666fbdf839e0a86affcd02defe87", "https://bcr.bazel.build/modules/rules_cc/0.0.17/MODULE.bazel": "2ae1d8f4238ec67d7185d8861cb0a2cdf4bc608697c331b95bf990e69b62e64a", "https://bcr.bazel.build/modules/rules_cc/0.0.2/MODULE.bazel": "6915987c90970493ab97393024c156ea8fb9f3bea953b2f3ec05c34f19b5695c", "https://bcr.bazel.build/modules/rules_cc/0.0.5/MODULE.bazel": "be41f87587998fe8890cd82ea4e848ed8eb799e053c224f78f3ff7fe1a1d9b74", "https://bcr.bazel.build/modules/rules_cc/0.0.6/MODULE.bazel": "abf360251023dfe3efcef65ab9d56beefa8394d4176dd29529750e1c57eaa33f", "https://bcr.bazel.build/modules/rules_cc/0.0.8/MODULE.bazel": "964c85c82cfeb6f3855e6a07054fdb159aced38e99a5eecf7bce9d53990afa3e", "https://bcr.bazel.build/modules/rules_cc/0.0.9/MODULE.bazel": "836e76439f354b89afe6a911a7adf59a6b2518fafb174483ad78a2a2fde7b1c5", "https://bcr.bazel.build/modules/rules_cc/0.1.1/MODULE.bazel": "2f0222a6f229f0bf44cd711dc13c858dad98c62d52bd51d8fc3a764a83125513", "https://bcr.bazel.build/modules/rules_cc/0.1.1/source.json": "d61627377bd7dd1da4652063e368d9366fc9a73920bfa396798ad92172cf645c", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/MODULE.bazel": "b9527010e5fef060af92b6724edb3691970a5b1f76f74b21d39f7d433641be60", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/source.json": "9300e71df0cdde0952f10afff1401fa664e9fc5d9ae6204660ba1b158d90d6a6", "https://bcr.bazel.build/modules/rules_foreign_cc/0.9.0/MODULE.bazel": "c9e8c682bf75b0e7c704166d79b599f93b72cfca5ad7477df596947891feeef6", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/MODULE.bazel": "40c97d1144356f52905566c55811f13b299453a14ac7769dfba2ac38192337a8", "https://bcr.bazel.build/modules/rules_go/0.33.0/MODULE.bazel": "a2b11b64cd24bf94f57454f53288a5dacfe6cb86453eee7761b7637728c1910c", "https://bcr.bazel.build/modules/rules_go/0.38.1/MODULE.bazel": "fb8e73dd3b6fc4ff9d260ceacd830114891d49904f5bda1c16bc147bcc254f71", "https://bcr.bazel.build/modules/rules_go/0.39.1/MODULE.bazel": "d34fb2a249403a5f4339c754f1e63dc9e5ad70b47c5e97faee1441fc6636cd61", "https://bcr.bazel.build/modules/rules_go/0.41.0/MODULE.bazel": "55861d8e8bb0e62cbd2896f60ff303f62ffcb0eddb74ecb0e5c0cbe36fc292c8", "https://bcr.bazel.build/modules/rules_go/0.42.0/MODULE.bazel": "8cfa875b9aa8c6fce2b2e5925e73c1388173ea3c32a0db4d2b4804b453c14270", "https://bcr.bazel.build/modules/rules_go/0.45.1/MODULE.bazel": "6d7884f0edf890024eba8ab31a621faa98714df0ec9d512389519f0edff0281a", "https://bcr.bazel.build/modules/rules_go/0.46.0/MODULE.bazel": "3477df8bdcc49e698b9d25f734c4f3a9f5931ff34ee48a2c662be168f5f2d3fd", "https://bcr.bazel.build/modules/rules_go/0.48.0/MODULE.bazel": "d00ebcae0908ee3f5e6d53f68677a303d6d59a77beef879598700049c3980a03", "https://bcr.bazel.build/modules/rules_go/0.50.1/MODULE.bazel": "b91a308dc5782bb0a8021ad4330c81fea5bda77f96b9e4c117b9b9c8f6665ee0", "https://bcr.bazel.build/modules/rules_go/0.52.0/MODULE.bazel": "0cf080a2706aa8fc9abf64286cee60fdf0238db37b7f1793b0f7d550d59ea3ae", "https://bcr.bazel.build/modules/rules_go/0.53.0/MODULE.bazel": "a4ed760d3ac0dbc0d7b967631a9a3fd9100d28f7d9fcf214b4df87d4bfff5f9a", "https://bcr.bazel.build/modules/rules_go/0.56.1/MODULE.bazel": "d5b835c548ac917345f1780cd2da52edc1130a908fe091c92096895303ae78a0", "https://bcr.bazel.build/modules/rules_go/0.56.1/source.json": "0c902f7272e8d4e47e459af97be472bc19dadbbe6023a0719d1adce8483ac75a", "https://bcr.bazel.build/modules/rules_java/4.0.0/MODULE.bazel": "5a78a7ae82cd1a33cef56dc578c7d2a46ed0dca12643ee45edbb8417899e6f74", "https://bcr.bazel.build/modules/rules_java/5.1.0/MODULE.bazel": "324b6478b0343a3ce7a9add8586ad75d24076d6d43d2f622990b9c1cfd8a1b15", "https://bcr.bazel.build/modules/rules_java/5.3.5/MODULE.bazel": "a4ec4f2db570171e3e5eb753276ee4b389bae16b96207e9d3230895c99644b86", "https://bcr.bazel.build/modules/rules_java/5.5.0/MODULE.bazel": "486ad1aa15cdc881af632b4b1448b0136c76025a1fe1ad1b65c5899376b83a50", "https://bcr.bazel.build/modules/rules_java/6.0.0/MODULE.bazel": "8a43b7df601a7ec1af61d79345c17b31ea1fedc6711fd4abfd013ea612978e39", "https://bcr.bazel.build/modules/rules_java/6.3.0/MODULE.bazel": "a97c7678c19f236a956ad260d59c86e10a463badb7eb2eda787490f4c969b963", "https://bcr.bazel.build/modules/rules_java/6.4.0/MODULE.bazel": "e986a9fe25aeaa84ac17ca093ef13a4637f6107375f64667a15999f77db6c8f6", "https://bcr.bazel.build/modules/rules_java/6.5.2/MODULE.bazel": "1d440d262d0e08453fa0c4d8f699ba81609ed0e9a9a0f02cd10b3e7942e61e31", "https://bcr.bazel.build/modules/rules_java/7.1.0/MODULE.bazel": "30d9135a2b6561c761bd67bd4990da591e6bdc128790ce3e7afd6a3558b2fb64", "https://bcr.bazel.build/modules/rules_java/7.10.0/MODULE.bazel": "530c3beb3067e870561739f1144329a21c851ff771cd752a49e06e3dc9c2e71a", "https://bcr.bazel.build/modules/rules_java/7.12.2/MODULE.bazel": "579c505165ee757a4280ef83cda0150eea193eed3bef50b1004ba88b99da6de6", "https://bcr.bazel.build/modules/rules_java/7.2.0/MODULE.bazel": "06c0334c9be61e6cef2c8c84a7800cef502063269a5af25ceb100b192453d4ab", "https://bcr.bazel.build/modules/rules_java/7.3.2/MODULE.bazel": "50dece891cfdf1741ea230d001aa9c14398062f2b7c066470accace78e412bc2", "https://bcr.bazel.build/modules/rules_java/7.4.0/MODULE.bazel": "a592852f8a3dd539e82ee6542013bf2cadfc4c6946be8941e189d224500a8934", "https://bcr.bazel.build/modules/rules_java/7.6.1/MODULE.bazel": "2f14b7e8a1aa2f67ae92bc69d1ec0fa8d9f827c4e17ff5e5f02e91caa3b2d0fe", "https://bcr.bazel.build/modules/rules_java/8.12.0/MODULE.bazel": "8e6590b961f2defdfc2811c089c75716cb2f06c8a4edeb9a8d85eaa64ee2a761", "https://bcr.bazel.build/modules/rules_java/8.15.1/MODULE.bazel": "5071eebf0fd602ab0617f846e0e0d8f388d66c961513c736e0ac4a1dcde3ff2c", "https://bcr.bazel.build/modules/rules_java/8.15.1/source.json": "e48286d5819767bc5b3d457539ae7f94e28a9b3e55d092d5c47176cb6a2a289b", "https://bcr.bazel.build/modules/rules_java/8.3.2/MODULE.bazel": "7336d5511ad5af0b8615fdc7477535a2e4e723a357b6713af439fe8cf0195017", "https://bcr.bazel.build/modules/rules_java/8.5.1/MODULE.bazel": "d8a9e38cc5228881f7055a6079f6f7821a073df3744d441978e7a43e20226939", "https://bcr.bazel.build/modules/rules_java/8.6.1/MODULE.bazel": "f4808e2ab5b0197f094cabce9f4b006a27766beb6a9975931da07099560ca9c2", "https://bcr.bazel.build/modules/rules_jvm_external/4.4.2/MODULE.bazel": "a56b85e418c83eb1839819f0b515c431010160383306d13ec21959ac412d2fe7", "https://bcr.bazel.build/modules/rules_jvm_external/5.1/MODULE.bazel": "33f6f999e03183f7d088c9be518a63467dfd0be94a11d0055fe2d210f89aa909", "https://bcr.bazel.build/modules/rules_jvm_external/5.2/MODULE.bazel": "d9351ba35217ad0de03816ef3ed63f89d411349353077348a45348b096615036", "https://bcr.bazel.build/modules/rules_jvm_external/5.3/MODULE.bazel": "bf93870767689637164657731849fb887ad086739bd5d360d90007a581d5527d", "https://bcr.bazel.build/modules/rules_jvm_external/6.0/MODULE.bazel": "37c93a5a78d32e895d52f86a8d0416176e915daabd029ccb5594db422e87c495", "https://bcr.bazel.build/modules/rules_jvm_external/6.1/MODULE.bazel": "75b5fec090dbd46cf9b7d8ea08cf84a0472d92ba3585b476f44c326eda8059c4", "https://bcr.bazel.build/modules/rules_jvm_external/6.3/MODULE.bazel": "c998e060b85f71e00de5ec552019347c8bca255062c990ac02d051bb80a38df0", "https://bcr.bazel.build/modules/rules_jvm_external/6.7/MODULE.bazel": "e717beabc4d091ecb2c803c2d341b88590e9116b8bf7947915eeb33aab4f96dd", "https://bcr.bazel.build/modules/rules_jvm_external/6.8/MODULE.bazel": "b5afe861e867e4c8e5b88e401cb7955bd35924258f97b1862cc966cbcf4f1a62", "https://bcr.bazel.build/modules/rules_jvm_external/6.8/source.json": "c85e553d5ac17f7825cd85b9cceb500c64f9e44f0e93c7887469e430c4ae9eff", "https://bcr.bazel.build/modules/rules_kotlin/1.9.0/MODULE.bazel": "ef85697305025e5a61f395d4eaede272a5393cee479ace6686dba707de804d59", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/MODULE.bazel": "d269a01a18ee74d0335450b10f62c9ed81f2321d7958a2934e44272fe82dcef3", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/source.json": "2faa4794364282db7c06600b7e5e34867a564ae91bda7cae7c29c64e9466b7d5", "https://bcr.bazel.build/modules/rules_license/0.0.3/MODULE.bazel": "627e9ab0247f7d1e05736b59dbb1b6871373de5ad31c3011880b4133cafd4bd0", "https://bcr.bazel.build/modules/rules_license/0.0.7/MODULE.bazel": "088fbeb0b6a419005b89cf93fe62d9517c0a2b8bb56af3244af65ecfe37e7d5d", "https://bcr.bazel.build/modules/rules_license/1.0.0/MODULE.bazel": "a7fda60eefdf3d8c827262ba499957e4df06f659330bbe6cdbdb975b768bb65c", "https://bcr.bazel.build/modules/rules_license/1.0.0/source.json": "a52c89e54cc311196e478f8382df91c15f7a2bfdf4c6cd0e2675cc2ff0b56efb", "https://bcr.bazel.build/modules/rules_pkg/0.7.0/MODULE.bazel": "df99f03fc7934a4737122518bb87e667e62d780b610910f0447665a7e2be62dc", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/MODULE.bazel": "5b1df97dbc29623bccdf2b0dcd0f5cb08e2f2c9050aab1092fd39a41e82686ff", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/source.json": "bd82e5d7b9ce2d31e380dd9f50c111d678c3bdaca190cb76b0e1c71b05e1ba8a", "https://bcr.bazel.build/modules/rules_proto/4.0.0/MODULE.bazel": "a7a7b6ce9bee418c1a760b3d84f83a299ad6952f9903c67f19e4edd964894e06", "https://bcr.bazel.build/modules/rules_proto/5.3.0-21.7/MODULE.bazel": "e8dff86b0971688790ae75528fe1813f71809b5afd57facb44dad9e8eca631b7", "https://bcr.bazel.build/modules/rules_proto/6.0.0-rc1/MODULE.bazel": "1e5b502e2e1a9e825eef74476a5a1ee524a92297085015a052510b09a1a09483", "https://bcr.bazel.build/modules/rules_proto/6.0.0/MODULE.bazel": "b531d7f09f58dce456cd61b4579ce8c86b38544da75184eadaf0a7cb7966453f", "https://bcr.bazel.build/modules/rules_proto/6.0.2/MODULE.bazel": "ce916b775a62b90b61888052a416ccdda405212b6aaeb39522f7dc53431a5e73", "https://bcr.bazel.build/modules/rules_proto/7.0.2/MODULE.bazel": "bf81793bd6d2ad89a37a40693e56c61b0ee30f7a7fdbaf3eabbf5f39de47dea2", "https://bcr.bazel.build/modules/rules_proto/7.0.2/source.json": "1e5e7260ae32ef4f2b52fd1d0de8d03b606a44c91b694d2f1afb1d3b28a48ce1", "https://bcr.bazel.build/modules/rules_python/0.10.2/MODULE.bazel": "cc82bc96f2997baa545ab3ce73f196d040ffb8756fd2d66125a530031cd90e5f", "https://bcr.bazel.build/modules/rules_python/0.20.0/MODULE.bazel": "bfe14d17f20e3fe900b9588f526f52c967a6f281e47a1d6b988679bd15082286", "https://bcr.bazel.build/modules/rules_python/0.22.0/MODULE.bazel": "b8057bafa11a9e0f4b08fc3b7cd7bee0dcbccea209ac6fc9a3ff051cd03e19e9", "https://bcr.bazel.build/modules/rules_python/0.22.1/MODULE.bazel": "26114f0c0b5e93018c0c066d6673f1a2c3737c7e90af95eff30cfee38d0bbac7", "https://bcr.bazel.build/modules/rules_python/0.23.1/MODULE.bazel": "49ffccf0511cb8414de28321f5fcf2a31312b47c40cc21577144b7447f2bf300", "https://bcr.bazel.build/modules/rules_python/0.25.0/MODULE.bazel": "72f1506841c920a1afec76975b35312410eea3aa7b63267436bfb1dd91d2d382", "https://bcr.bazel.build/modules/rules_python/0.28.0/MODULE.bazel": "cba2573d870babc976664a912539b320cbaa7114cd3e8f053c720171cde331ed", "https://bcr.bazel.build/modules/rules_python/0.29.0/MODULE.bazel": "2ac8cd70524b4b9ec49a0b8284c79e4cd86199296f82f6e0d5da3f783d660c82", "https://bcr.bazel.build/modules/rules_python/0.31.0/MODULE.bazel": "93a43dc47ee570e6ec9f5779b2e64c1476a6ce921c48cc9a1678a91dd5f8fd58", "https://bcr.bazel.build/modules/rules_python/0.33.2/MODULE.bazel": "3e036c4ad8d804a4dad897d333d8dce200d943df4827cb849840055be8d2e937", "https://bcr.bazel.build/modules/rules_python/0.37.1/MODULE.bazel": "3faeb2d9fa0a81f8980643ee33f212308f4d93eea4b9ce6f36d0b742e71e9500", "https://bcr.bazel.build/modules/rules_python/0.4.0/MODULE.bazel": "9208ee05fd48bf09ac60ed269791cf17fb343db56c8226a720fbb1cdf467166c", "https://bcr.bazel.build/modules/rules_python/0.40.0/MODULE.bazel": "9d1a3cd88ed7d8e39583d9ffe56ae8a244f67783ae89b60caafc9f5cf318ada7", "https://bcr.bazel.build/modules/rules_python/1.0.0/MODULE.bazel": "898a3d999c22caa585eb062b600f88654bf92efb204fa346fb55f6f8edffca43", "https://bcr.bazel.build/modules/rules_python/1.0.0/source.json": "b0162a65c6312e45e7912e39abd1a7f8856c2c7e41ecc9b6dc688a6f6400a917", "https://bcr.bazel.build/modules/rules_shell/0.2.0/MODULE.bazel": "fda8a652ab3c7d8fee214de05e7a9916d8b28082234e8d2c0094505c5268ed3c", "https://bcr.bazel.build/modules/rules_shell/0.3.0/MODULE.bazel": "de4402cd12f4cc8fda2354fce179fdb068c0b9ca1ec2d2b17b3e21b24c1a937b", "https://bcr.bazel.build/modules/rules_shell/0.3.0/source.json": "c55ed591aa5009401ddf80ded9762ac32c358d2517ee7820be981e2de9756cf3", "https://bcr.bazel.build/modules/rules_swift/1.16.0/MODULE.bazel": "4a09f199545a60d09895e8281362b1ff3bb08bbde69c6fc87aff5b92fcc916ca", "https://bcr.bazel.build/modules/rules_swift/1.18.0/MODULE.bazel": "a6aba73625d0dc64c7b4a1e831549b6e375fbddb9d2dde9d80c9de6ec45b24c9", "https://bcr.bazel.build/modules/rules_swift/2.1.1/MODULE.bazel": "494900a80f944fc7aa61500c2073d9729dff0b764f0e89b824eb746959bc1046", "https://bcr.bazel.build/modules/rules_swift/2.1.1/source.json": "40fc69dfaac64deddbb75bd99cdac55f4427d9ca0afbe408576a65428427a186", "https://bcr.bazel.build/modules/stardoc/0.5.1/MODULE.bazel": "1a05d92974d0c122f5ccf09291442580317cdd859f07a8655f1db9a60374f9f8", "https://bcr.bazel.build/modules/stardoc/0.5.3/MODULE.bazel": "c7f6948dae6999bf0db32c1858ae345f112cacf98f174c7a8bb707e41b974f1c", "https://bcr.bazel.build/modules/stardoc/0.5.6/MODULE.bazel": "c43dabc564990eeab55e25ed61c07a1aadafe9ece96a4efabb3f8bf9063b71ef", "https://bcr.bazel.build/modules/stardoc/0.6.2/MODULE.bazel": "7060193196395f5dd668eda046ccbeacebfd98efc77fed418dbe2b82ffaa39fd", "https://bcr.bazel.build/modules/stardoc/0.7.0/MODULE.bazel": "05e3d6d30c099b6770e97da986c53bd31844d7f13d41412480ea265ac9e8079c", "https://bcr.bazel.build/modules/stardoc/0.7.1/MODULE.bazel": "3548faea4ee5dda5580f9af150e79d0f6aea934fc60c1cc50f4efdd9420759e7", "https://bcr.bazel.build/modules/stardoc/0.7.2/MODULE.bazel": "fc152419aa2ea0f51c29583fab1e8c99ddefd5b3778421845606ee628629e0e5", "https://bcr.bazel.build/modules/stardoc/0.7.2/source.json": "58b029e5e901d6802967754adf0a9056747e8176f017cfe3607c0851f4d42216", "https://bcr.bazel.build/modules/swift_argument_parser/1.3.1.1/MODULE.bazel": "5e463fbfba7b1701d957555ed45097d7f984211330106ccd1352c6e0af0dcf91", "https://bcr.bazel.build/modules/swift_argument_parser/1.3.1.1/source.json": "32bd87e5f4d7acc57c5b2ff7c325ae3061d5e242c0c4c214ae87e0f1c13e54cb", "https://bcr.bazel.build/modules/upb/0.0.0-20211020-160625a/MODULE.bazel": "6cced416be2dc5b9c05efd5b997049ba795e5e4e6fafbe1624f4587767638928", "https://bcr.bazel.build/modules/upb/0.0.0-20220923-a547704/MODULE.bazel": "7298990c00040a0e2f121f6c32544bab27d4452f80d9ce51349b1a28f3005c43", "https://bcr.bazel.build/modules/upb/0.0.0-20230516-61a97ef/MODULE.bazel": "c0df5e35ad55e264160417fd0875932ee3c9dda63d9fccace35ac62f45e1b6f9", "https://bcr.bazel.build/modules/upb/0.0.0-20230907-e7430e6/MODULE.bazel": "3a7dedadf70346e678dc059dbe44d05cbf3ab17f1ce43a1c7a42edc7cbf93fd9", "https://bcr.bazel.build/modules/xds/0.0.0-20240423-555b57e/MODULE.bazel": "cea509976a77e34131411684ef05a1d6ad194dd71a8d5816643bc5b0af16dc0f", "https://bcr.bazel.build/modules/xds/0.0.0-20240423-555b57e/source.json": "7227e1fcad55f3f3cab1a08691ecd753cb29cc6380a47bc650851be9f9ad6d20", "https://bcr.bazel.build/modules/zlib/1.2.11/MODULE.bazel": "07b389abc85fdbca459b69e2ec656ae5622873af3f845e1c9d80fe179f3effa0", "https://bcr.bazel.build/modules/zlib/1.2.12/MODULE.bazel": "3b1a8834ada2a883674be8cbd36ede1b6ec481477ada359cd2d3ddc562340b27", "https://bcr.bazel.build/modules/zlib/1.2.13/MODULE.bazel": "aa6deb1b83c18ffecd940c4119aff9567cd0a671d7bba756741cb2ef043a29d5", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.1/MODULE.bazel": "6a9fe6e3fc865715a7be9823ce694ceb01e364c35f7a846bf0d2b34762bc066b", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.3/MODULE.bazel": "af322bc08976524477c79d1e45e241b6efbeb918c497e8840b8ab116802dda79", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.5/MODULE.bazel": "eec517b5bbe5492629466e11dae908d043364302283de25581e3eb944326c4ca", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.5/source.json": "22bc55c47af97246cfc093d0acf683a7869377de362b5d1c552c2c2e16b7a806", "https://bcr.bazel.build/modules/zlib/1.3.1/MODULE.bazel": "751c9940dcfe869f5f7274e1295422a34623555916eb98c174c1e945594bf198", "https://bcr.bazel.build/modules/zlib/1.3/MODULE.bazel": "6a9c02f19a24dcedb05572b2381446e27c272cd383aed11d41d99da9e3167a72"}, "selectedYankedVersions": {}, "moduleExtensions": {"@@apple_rules_lint+//lint:extensions.bzl%linter": {"general": {"bzlTransitiveDigest": "g7izj5kLCmsajh8IospHh4ZQ35dyM0FIrA8D4HapAsM=", "usagesDigest": "jTOwxDX06coCna6ZG2TdoE5lX5fW4b+uJpv1bucdx+Q=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"apple_linters": {"repoRuleId": "@@apple_rules_lint+//lint/private:register_linters.bzl%register_linters", "attributes": {"linters": {}}}}, "recordedRepoMappingEntries": []}}, "@@apple_support+//crosstool:setup.bzl%apple_cc_configure_extension": {"general": {"bzlTransitiveDigest": "E970FlMbwpgJPdPUQzatKh6BMfeE0ZpWABvwshh7Tmg=", "usagesDigest": "EJfdUgbJdIRboG70S9dz8HhGVkL2/s3qFQ9xht8y2Rs=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_config_apple_cc_toolchains": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf_toolchains", "attributes": {}}, "local_config_apple_cc": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support+", "bazel_tools", "bazel_tools"], ["bazel_tools", "rules_cc", "rules_cc+"]]}}, "@@cel-spec+//:extensions.bzl%non_module_dependencies": {"general": {"bzlTransitiveDigest": "jrNi95LSVI0rFlx/mBoUG/rvd+z6dHU45YdcnsXyI2g=", "usagesDigest": "HFQJtQrL9nKaFZEjgwaHVMHALMW+cafu696xy7J4ueM=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_google_googleapis": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "bd8e735d881fb829751ecb1a77038dda4a8d274c45490cb9fcf004583ee10571", "strip_prefix": "googleapis-07c27163ac591955d736f3057b1619ece66f5b99", "urls": ["https://github.com/googleapis/googleapis/archive/07c27163ac591955d736f3057b1619ece66f5b99.tar.gz"]}}}, "recordedRepoMappingEntries": [["cel-spec+", "bazel_tools", "bazel_tools"]]}}, "@@cel-spec+//:googleapis_ext.bzl%googleapis_ext": {"general": {"bzlTransitiveDigest": "yun2jmsomFi3bs5bjQWXApBzqQf66zBJ39JEBYigzdc=", "usagesDigest": "Ek7VfZ+tuyRBx/1h5wcmtnW9EGpOb0dkXUwBluZbD8k=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_google_googleapis_imports": {"repoRuleId": "@@cel-spec++non_module_dependencies+com_google_googleapis//:repository_rules.bzl%switched_rules", "attributes": {"rules": {"proto_library_with_info": ["", ""], "moved_proto_library": ["", ""], "java_proto_library": ["", ""], "java_grpc_library": ["", ""], "java_gapic_library": ["", ""], "java_gapic_test": ["", ""], "java_gapic_assembly_gradle_pkg": ["", ""], "py_proto_library": ["", ""], "py_grpc_library": ["", ""], "py_gapic_library": ["", ""], "py_test": ["", ""], "py_gapic_assembly_pkg": ["", ""], "py_import": ["", ""], "go_proto_library": ["", ""], "go_library": ["", ""], "go_test": ["", ""], "go_gapic_library": ["", ""], "go_gapic_assembly_pkg": ["", ""], "cc_proto_library": ["native.cc_proto_library", ""], "cc_grpc_library": ["", ""], "cc_gapic_library": ["", ""], "php_proto_library": ["", "php_proto_library"], "php_grpc_library": ["", "php_grpc_library"], "php_gapic_library": ["", "php_gapic_library"], "php_gapic_assembly_pkg": ["", "php_gapic_assembly_pkg"], "nodejs_gapic_library": ["", "typescript_gapic_library"], "nodejs_gapic_assembly_pkg": ["", "typescript_gapic_assembly_pkg"], "ruby_proto_library": ["", ""], "ruby_grpc_library": ["", ""], "ruby_ads_gapic_library": ["", ""], "ruby_cloud_gapic_library": ["", ""], "ruby_gapic_assembly_pkg": ["", ""], "csharp_proto_library": ["", ""], "csharp_grpc_library": ["", ""], "csharp_gapic_library": ["", ""], "csharp_gapic_assembly_pkg": ["", ""]}}}}, "recordedRepoMappingEntries": [["cel-spec+", "com_google_googleapis", "cel-spec++non_module_dependencies+com_google_googleapis"]]}}, "@@envoy_api+//bazel:repositories.bzl%non_module_deps": {"general": {"bzlTransitiveDigest": "zoTuufNmmcfLHM15yon0+OOm6uctbBTuxM0dSmD2QU4=", "usagesDigest": "cxAa0VVo9d210JBUBw6wpuGp8jg+ltqw3tzH0tPtIEg=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"prometheus_metrics_model": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/prometheus/client_model/archive/v0.6.1.tar.gz"], "sha256": "b9b690bc35d80061f255faa7df7621eae39fe157179ccd78ff6409c3b004f05e", "strip_prefix": "client_model-0.6.1", "build_file_content": "\nload(\"@envoy_api//bazel:api_build_system.bzl\", \"api_cc_py_proto_library\")\nload(\"@io_bazel_rules_go//proto:def.bzl\", \"go_proto_library\")\n\napi_cc_py_proto_library(\n    name = \"client_model\",\n    srcs = [\n        \"io/prometheus/client/metrics.proto\",\n    ],\n    visibility = [\"//visibility:public\"],\n)\n\ngo_proto_library(\n    name = \"client_model_go_proto\",\n    importpath = \"github.com/prometheus/client_model/go\",\n    proto = \":client_model\",\n    visibility = [\"//visibility:public\"],\n)\n"}}, "com_github_bufbuild_buf": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/bufbuild/buf/releases/download/v1.47.2/buf-Linux-x86_64.tar.gz"], "sha256": "39716cfe0185df3cba21f66ec739620ffb6876c48b2da4338a8c68c290c9b116", "strip_prefix": "buf", "build_file_content": "\npackage(\n    default_visibility = [\"//visibility:public\"],\n)\n\nfilegroup(\n    name = \"buf\",\n    srcs = [\n        \"@com_github_bufbuild_buf//:bin/buf\",\n    ],\n    tags = [\"manual\"], # buf is downloaded as a linux binary; tagged manual to prevent build for non-linux users\n)\n"}}, "envoy_toolshed": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/envoyproxy/toolshed/archive/bazel-v0.2.0.tar.gz"], "sha256": "ef5e95580c41f6805beec197d9a4f6683550f4bfc1e1c678449b6d205dbf000b", "strip_prefix": "toolshed-bazel-v0.2.0/bazel"}}}, "recordedRepoMappingEntries": [["envoy_api+", "bazel_tools", "bazel_tools"], ["envoy_api+", "envoy_api", "envoy_api+"]]}}, "@@googleapis+//:extensions.bzl%switched_rules": {"general": {"bzlTransitiveDigest": "vG6fuTzXD8MMvHWZEQud0MMH7eoC4GXY0va7VrFFh04=", "usagesDigest": "CAry9PE//aJqVo25arsA0H+N+tS8r5uv2OoDA+5BYEM=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_google_googleapis_imports": {"repoRuleId": "@@googleapis+//:repository_rules.bzl%switched_rules", "attributes": {"rules": {"proto_library_with_info": ["", ""], "moved_proto_library": ["", ""], "java_proto_library": ["", ""], "java_grpc_library": ["", ""], "java_gapic_library": ["", ""], "java_gapic_test": ["", ""], "java_gapic_assembly_gradle_pkg": ["", ""], "py_proto_library": ["", ""], "py_grpc_library": ["", ""], "py_gapic_library": ["", ""], "py_test": ["", ""], "py_gapic_assembly_pkg": ["", ""], "py_import": ["", ""], "go_proto_library": ["", ""], "go_grpc_library": ["", ""], "go_library": ["", ""], "go_test": ["", ""], "go_gapic_library": ["", ""], "go_gapic_assembly_pkg": ["", ""], "cc_proto_library": ["", ""], "cc_grpc_library": ["", ""], "cc_gapic_library": ["", ""], "php_proto_library": ["", "php_proto_library"], "php_grpc_library": ["", "php_grpc_library"], "php_gapic_library": ["", "php_gapic_library"], "php_gapic_assembly_pkg": ["", "php_gapic_assembly_pkg"], "nodejs_gapic_library": ["", "typescript_gapic_library"], "nodejs_gapic_assembly_pkg": ["", "typescript_gapic_assembly_pkg"], "ruby_proto_library": ["", ""], "ruby_grpc_library": ["", ""], "ruby_ads_gapic_library": ["", ""], "ruby_cloud_gapic_library": ["", ""], "ruby_gapic_assembly_pkg": ["", ""], "csharp_proto_library": ["", ""], "csharp_grpc_library": ["", ""], "csharp_gapic_library": ["", ""], "csharp_gapic_assembly_pkg": ["", ""]}}}}, "recordedRepoMappingEntries": []}}, "@@grpc+//bazel:grpc_deps.bzl%grpc_repo_deps_ext": {"general": {"bzlTransitiveDigest": "wvr73CFHOetddekmNcxDr/OWMMC9dhm5k1iBwuUv8go=", "usagesDigest": "JRy5mn8l60PheiZTvsAmif/CiZhfYCkdmx6ULADtUrg=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"google_cloud_cpp": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "7ca7f583b60d2aa1274411fed3b9fb3887119b2e84244bb3fc69ea1db819e4e5", "strip_prefix": "google-cloud-cpp-2.16.0", "urls": ["https://storage.googleapis.com/grpc-bazel-mirror/github.com/googleapis/google-cloud-cpp/archive/refs/tags/v2.16.0.tar.gz", "https://github.com/googleapis/google-cloud-cpp/archive/refs/tags/v2.16.0.tar.gz"]}}}, "recordedRepoMappingEntries": [["grpc+", "bazel_tools", "bazel_tools"], ["grpc+", "com_github_grpc_grpc", "grpc+"]]}}, "@@pybind11_bazel+//:internal_configure.bzl%internal_configure_extension": {"general": {"bzlTransitiveDigest": "vyKH4VZgvJxNRuv2Dn3yUi/i7TcjLFk2up5SgTbIUY8=", "usagesDigest": "D1r3lfzMuUBFxgG8V6o0bQTLMk3GkaGOaPzw53wrwyw=", "recordedFileInputs": {"@@pybind11_bazel+//MODULE.bazel": "e6f4c20442eaa7c90d7190d8dc539d0ab422f95c65a57cc59562170c58ae3d34"}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"pybind11": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file": "@@pybind11_bazel+//:pybind11-BUILD.bazel", "strip_prefix": "pybind11-2.12.0", "urls": ["https://github.com/pybind/pybind11/archive/v2.12.0.zip"]}}}, "recordedRepoMappingEntries": [["pybind11_bazel+", "bazel_tools", "bazel_tools"]]}}, "@@rules_apple+//apple:apple.bzl%provisioning_profile_repository_extension": {"general": {"bzlTransitiveDigest": "0owyZzufrcndNC+tsd4gvFRHhsHMpOkROsveC5nuiXc=", "usagesDigest": "vsJl8Rw5NL+5Ag2wdUDoTeRF/5klkXO8545Iy7U1Q08=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_provisioning_profiles": {"repoRuleId": "@@rules_apple+//apple/internal:local_provisioning_profiles.bzl%provisioning_profile_repository", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support+", "bazel_skylib", "bazel_skylib+"], ["bazel_tools", "rules_cc", "rules_cc+"], ["rules_apple+", "bazel_skylib", "bazel_skylib+"], ["rules_apple+", "bazel_tools", "bazel_tools"], ["rules_apple+", "build_bazel_apple_support", "apple_support+"], ["rules_apple+", "build_bazel_rules_swift", "rules_swift+"], ["rules_cc+", "bazel_tools", "bazel_tools"], ["rules_cc+", "rules_cc", "rules_cc+"], ["rules_swift+", "bazel_skylib", "bazel_skylib+"], ["rules_swift+", "bazel_tools", "bazel_tools"], ["rules_swift+", "build_bazel_apple_support", "apple_support+"], ["rules_swift+", "build_bazel_rules_swift", "rules_swift+"], ["rules_swift+", "build_bazel_rules_swift_local_config", "rules_swift++non_module_deps+build_bazel_rules_swift_local_config"]]}}, "@@rules_apple+//apple:extensions.bzl%non_module_deps": {"general": {"bzlTransitiveDigest": "CwnxZSval4booBn3AH4F4AU4C9LwBCZuIK9TxDRaO0k=", "usagesDigest": "M3VqFpeTCo4qmrNKGZw0dxBHvTYDrfV3cscGzlSAhQ4=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"xctestrunner": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/google/xctestrunner/archive/b7698df3d435b6491b4b4c0f9fc7a63fbed5e3a6.tar.gz"], "strip_prefix": "xctestrunner-b7698df3d435b6491b4b4c0f9fc7a63fbed5e3a6", "sha256": "ae3a063c985a8633cb7eb566db21656f8db8eb9a0edb8c182312c7f0db53730d"}}}, "recordedRepoMappingEntries": [["rules_apple+", "bazel_tools", "bazel_tools"]]}}, "@@rules_foreign_cc+//foreign_cc:extensions.bzl%tools": {"general": {"bzlTransitiveDigest": "ginC3lIGOKKivBi0nyv2igKvSiz42Thm8yaX2RwVaHg=", "usagesDigest": "9LXdVp01HkdYQT8gYPjYLO6VLVJHo9uFfxWaU1ymiRE=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"rules_foreign_cc_framework_toolchain_linux": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:linux_commands.bzl", "exec_compatible_with": ["@platforms//os:linux"]}}, "rules_foreign_cc_framework_toolchain_freebsd": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:freebsd_commands.bzl", "exec_compatible_with": ["@platforms//os:freebsd"]}}, "rules_foreign_cc_framework_toolchain_windows": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:windows_commands.bzl", "exec_compatible_with": ["@platforms//os:windows"]}}, "rules_foreign_cc_framework_toolchain_macos": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:macos_commands.bzl", "exec_compatible_with": ["@platforms//os:macos"]}}, "rules_foreign_cc_framework_toolchains": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository_hub", "attributes": {}}, "cmake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "f316b40053466f9a416adf981efda41b160ca859e97f6a484b447ea299ff26aa", "strip_prefix": "cmake-3.23.2", "urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2.tar.gz"]}}, "gnumake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "581f4d4e872da74b3941c874215898a7d35802f03732bdccee1d4a7979105d18", "strip_prefix": "make-4.4", "urls": ["https://mirror.bazel.build/ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz", "http://ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz"]}}, "ninja_build_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "31747ae633213f1eda3842686f83c2aa1412e0f5691d1c14dbbcc67fe7400cea", "strip_prefix": "ninja-1.11.1", "urls": ["https://github.com/ninja-build/ninja/archive/v1.11.1.tar.gz"]}}, "meson_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "exports_files([\"meson.py\"])\n\nfilegroup(\n    name = \"runtime\",\n    srcs = glob([\"mesonbuild/**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "strip_prefix": "meson-1.1.1", "url": "https://github.com/mesonbuild/meson/releases/download/1.1.1/meson-1.1.1.tar.gz"}}, "glib_dev": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nload(\"@rules_cc//cc:defs.bzl\", \"cc_library\")\n\ncc_import(\n    name = \"glib_dev\",\n    hdrs = glob([\"include/**\"]),\n    shared_library = \"@glib_runtime//:bin/libglib-2.0-0.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bdf18506df304d38be98a4b3f18055b8b8cca81beabecad0eece6ce95319c369", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib-dev_2.26.1-1_win64.zip"]}}, "glib_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"msvc_hdr\",\n    hdrs = [\"msvc_recommended_pragmas.h\"],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bc96f63112823b7d6c9f06572d2ad626ddac7eb452c04d762592197f6e07898e", "strip_prefix": "glib-2.26.1", "urls": ["https://download.gnome.org/sources/glib/2.26/glib-2.26.1.tar.gz"]}}, "glib_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nexports_files(\n    [\n        \"bin/libgio-2.0-0.dll\",\n        \"bin/libglib-2.0-0.dll\",\n        \"bin/libgmodule-2.0-0.dll\",\n        \"bin/libgobject-2.0-0.dll\",\n        \"bin/libgthread-2.0-0.dll\",\n    ],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "88d857087e86f16a9be651ee7021880b3f7ba050d34a1ed9f06113b8799cb973", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib_2.26.1-1_win64.zip"]}}, "gettext_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"gettext_runtime\",\n    shared_library = \"bin/libintl-8.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "1f4269c0e021076d60a54e98da6f978a3195013f6de21674ba0edbc339c5b079", "urls": ["https://download.gnome.org/binaries/win64/dependencies/gettext-runtime_0.18.1.1-2_win64.zip"]}}, "pkgconfig_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "6fc69c01688c9458a57eb9a1664c9aba372ccda420a02bf4429fe610e7e7d591", "strip_prefix": "pkg-config-0.29.2", "patches": ["@@rules_foreign_cc+//toolchains:pkgconfig-detectenv.patch", "@@rules_foreign_cc+//toolchains:pkgconfig-makefile-vc.patch"], "urls": ["https://pkgconfig.freedesktop.org/releases/pkg-config-0.29.2.tar.gz"]}}, "bazel_skylib": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz", "https://github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz"], "sha256": "f7be3474d42aae265405a592bb7da8e171919d74c16f082a5457840f06054728"}}, "rules_python": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "84aec9e21cc56fbc7f1335035a71c850d1b9b5cc6ff497306f84cced9a769841", "strip_prefix": "rules_python-0.23.1", "url": "https://github.com/bazelbuild/rules_python/archive/refs/tags/0.23.1.tar.gz"}}, "cmake-3.23.2-linux-aarch64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-aarch64.tar.gz"], "sha256": "f2654bf780b53f170bbbec44d8ac67d401d24788e590faa53036a89476efa91e", "strip_prefix": "cmake-3.23.2-linux-aarch64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-linux-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-x86_64.tar.gz"], "sha256": "aaced6f745b86ce853661a595bdac6c5314a60f8181b6912a0a4920acfa32708", "strip_prefix": "cmake-3.23.2-linux-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-macos-universal": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-macos-universal.tar.gz"], "sha256": "853a0f9af148c5ef47282ffffee06c4c9f257be2635936755f39ca13c3286c88", "strip_prefix": "cmake-3.23.2-macos-universal/CMake.app/Contents", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-i386": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-i386.zip"], "sha256": "6a4fcd6a2315b93cb23c93507efccacc30c449c2bf98f14d6032bb226c582e07", "strip_prefix": "cmake-3.23.2-windows-i386", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-x86_64.zip"], "sha256": "2329387f3166b84c25091c86389fb891193967740c9bcf01e7f6d3306f7ffda0", "strip_prefix": "cmake-3.23.2-windows-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake_3.23.2_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"cmake-3.23.2-linux-aarch64": ["@platforms//cpu:aarch64", "@platforms//os:linux"], "cmake-3.23.2-linux-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "cmake-3.23.2-macos-universal": ["@platforms//os:macos"], "cmake-3.23.2-windows-i386": ["@platforms//cpu:x86_32", "@platforms//os:windows"], "cmake-3.23.2-windows-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "cmake"}}, "ninja_1.11.1_linux": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-linux.zip"], "sha256": "b901ba96e486dce377f9a070ed4ef3f79deb45f4ffe2938f8e7ddc69cfb3df77", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_mac": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-mac.zip"], "sha256": "482ecb23c59ae3d4f158029112de172dd96bb0e97549c4b1ca32d8fad11f873e", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_win": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-win.zip"], "sha256": "524b344a1a9a55005eaf868d991e090ab8ce07fa109f1820d40e74642e289abc", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja.exe\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"ninja_1.11.1_linux": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "ninja_1.11.1_mac": ["@platforms//cpu:x86_64", "@platforms//os:macos"], "ninja_1.11.1_win": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "ninja"}}}, "recordedRepoMappingEntries": [["rules_foreign_cc+", "bazel_tools", "bazel_tools"], ["rules_foreign_cc+", "rules_foreign_cc", "rules_foreign_cc+"]]}}, "@@rules_kotlin+//src/main/starlark/core/repositories:bzlmod_setup.bzl%rules_kotlin_extensions": {"general": {"bzlTransitiveDigest": "hUTp2w+RUVdL7ma5esCXZJAFnX7vLbVfLd7FwnQI6bU=", "usagesDigest": "QI2z8ZUR+mqtbwsf2fLqYdJAkPOHdOV+tF2yVAUgRzw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_github_jetbrains_kotlin_git": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_compiler_git_repository", "attributes": {"urls": ["https://github.com/JetBrains/kotlin/releases/download/v1.9.23/kotlin-compiler-1.9.23.zip"], "sha256": "93137d3aab9afa9b27cb06a824c2324195c6b6f6179d8a8653f440f5bd58be88"}}, "com_github_jetbrains_kotlin": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_capabilities_repository", "attributes": {"git_repository_name": "com_github_jetbrains_kotlin_git", "compiler_version": "1.9.23"}}, "com_github_google_ksp": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:ksp.bzl%ksp_compiler_plugin_repository", "attributes": {"urls": ["https://github.com/google/ksp/releases/download/1.9.23-1.0.20/artifacts.zip"], "sha256": "ee0618755913ef7fd6511288a232e8fad24838b9af6ea73972a76e81053c8c2d", "strip_version": "1.9.23-1.0.20"}}, "com_github_pinterest_ktlint": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"sha256": "01b2e0ef893383a50dbeb13970fe7fa3be36ca3e83259e01649945b09d736985", "urls": ["https://github.com/pinterest/ktlint/releases/download/1.3.0/ktlint"], "executable": true}}, "rules_android": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "cd06d15dd8bb59926e4d65f9003bfc20f9da4b2519985c27e190cddc8b7a7806", "strip_prefix": "rules_android-0.1.1", "urls": ["https://github.com/bazelbuild/rules_android/archive/v0.1.1.zip"]}}}, "recordedRepoMappingEntries": [["rules_kotlin+", "bazel_tools", "bazel_tools"]]}}, "@@rules_python+//python/extensions:pip.bzl%pip": {"general": {"bzlTransitiveDigest": "fVqnDX/ayBF8O4w/EuHgvsLvUgjX6ho78MO65JdRliI=", "usagesDigest": "wXZClFE7MulBzzyBhPxsf/QBbMn7WPL40PcF0/yd9pg=", "recordedFileInputs": {"@@grpc+//requirements.bazel.txt": "4c8c19a2a8f22108bf29feb5cc2694eb0c7e0c82ba0364df27fe5f5e4d7936e5", "@@protoc-gen-validate+//python/requirements.txt": "519d7dac0ff11c4a855aaef022dc2998ece0669db3b481cdb9e5a5d88e57eb83", "@@rules_python+//tools/publish/requirements_darwin.txt": "2994136eab7e57b083c3de76faf46f70fad130bc8e7360a7fed2b288b69e79dc", "@@rules_python+//tools/publish/requirements_linux.txt": "8175b4c8df50ae2f22d1706961884beeb54e7da27bd2447018314a175981997d", "@@rules_python+//tools/publish/requirements_windows.txt": "7673adc71dc1a81d3661b90924d7a7c0fc998cd508b3cb4174337cef3f2de556"}, "recordedDirentsInputs": {}, "envVariables": {"RULES_PYTHON_REPO_DEBUG": null, "RULES_PYTHON_REPO_DEBUG_VERBOSITY": null}, "generatedRepoSpecs": {"grpc_python_dependencies_310_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_310_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_310_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_310_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_310_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_310_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_310_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_310_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_310_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_310_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_310_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_310_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_310_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_310_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "googleapis-common-protos==1.63.1"}}, "grpc_python_dependencies_310_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_310_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "idna==2.7"}}, "grpc_python_dependencies_310_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_310_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_310_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_310_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_310_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_310_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_310_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_310_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "opentelemetry-semantic-conventions==0.46b0"}}, "grpc_python_dependencies_310_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_310_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_310_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "protobuf>=5.27.1,<6.0dev"}}, "grpc_python_dependencies_310_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_310_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_310_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_310_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_310_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_310_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_310_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_310_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_310_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_310_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_310_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_310_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "grpc_python_dependencies_310", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_311_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_311_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_311_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_311_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_311_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_311_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_311_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_311_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_311_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_311_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_311_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_311_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_311_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_311_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "googleapis-common-protos==1.63.1"}}, "grpc_python_dependencies_311_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_311_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "idna==2.7"}}, "grpc_python_dependencies_311_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_311_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_311_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_311_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_311_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_311_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_311_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_311_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "opentelemetry-semantic-conventions==0.46b0"}}, "grpc_python_dependencies_311_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_311_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_311_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "protobuf>=5.27.1,<6.0dev"}}, "grpc_python_dependencies_311_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_311_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_311_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_311_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_311_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_311_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_311_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_311_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_311_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_311_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_311_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_311_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "grpc_python_dependencies_311", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_312_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_312_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_312_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_312_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_312_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_312_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_312_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_312_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_312_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_312_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_312_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_312_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_312_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_312_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "googleapis-common-protos==1.63.1"}}, "grpc_python_dependencies_312_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_312_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "idna==2.7"}}, "grpc_python_dependencies_312_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_312_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_312_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_312_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_312_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_312_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_312_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_312_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "opentelemetry-semantic-conventions==0.46b0"}}, "grpc_python_dependencies_312_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_312_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_312_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "protobuf>=5.27.1,<6.0dev"}}, "grpc_python_dependencies_312_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_312_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_312_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_312_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_312_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_312_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_312_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_312_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_312_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_312_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_312_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_312_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "grpc_python_dependencies_312", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_313_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_313_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_313_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_313_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_313_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_313_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_313_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_313_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_313_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_313_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_313_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_313_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_313_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_313_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "googleapis-common-protos==1.63.1"}}, "grpc_python_dependencies_313_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_313_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "idna==2.7"}}, "grpc_python_dependencies_313_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_313_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_313_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_313_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_313_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_313_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_313_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_313_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "opentelemetry-semantic-conventions==0.46b0"}}, "grpc_python_dependencies_313_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_313_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_313_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "protobuf>=5.27.1,<6.0dev"}}, "grpc_python_dependencies_313_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_313_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_313_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_313_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_313_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_313_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_313_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_313_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_313_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_313_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_313_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_313_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "grpc_python_dependencies_313", "requirement": "zope.interface==6.1"}}, "grpc_python_dependencies_39_absl_py": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "absl-py==1.4.0"}}, "grpc_python_dependencies_39_cachetools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "cachetools==5.3.2"}}, "grpc_python_dependencies_39_certifi": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "certifi==2023.7.22"}}, "grpc_python_dependencies_39_chardet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "chardet==3.0.4"}}, "grpc_python_dependencies_39_charset_normalizer": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "charset-normalizer==3.3.2"}}, "grpc_python_dependencies_39_coverage": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "coverage==4.5.4"}}, "grpc_python_dependencies_39_cython": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "cython==3.0.0"}}, "grpc_python_dependencies_39_deprecated": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "Deprecated==1.2.14"}}, "grpc_python_dependencies_39_gevent": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "gevent==22.08.0"}}, "grpc_python_dependencies_39_google_api_core": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-api-core==1.34.1"}}, "grpc_python_dependencies_39_google_auth": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-auth==2.23.4"}}, "grpc_python_dependencies_39_google_cloud_monitoring": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-cloud-monitoring==2.16.0"}}, "grpc_python_dependencies_39_google_cloud_trace": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "google-cloud-trace==1.11.3"}}, "grpc_python_dependencies_39_googleapis_common_protos": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "googleapis-common-protos==1.63.1"}}, "grpc_python_dependencies_39_greenlet": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "greenlet==1.1.3.post0"}}, "grpc_python_dependencies_39_idna": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "idna==2.7"}}, "grpc_python_dependencies_39_importlib_metadata": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "importlib-metadata==6.11.0"}}, "grpc_python_dependencies_39_oauth2client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "oauth2client==4.1.0"}}, "grpc_python_dependencies_39_opencensus_context": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opencensus-context==0.1.3"}}, "grpc_python_dependencies_39_opentelemetry_api": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-api==1.25.0"}}, "grpc_python_dependencies_39_opentelemetry_exporter_prometheus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-exporter-prometheus==0.46b0"}}, "grpc_python_dependencies_39_opentelemetry_resourcedetector_gcp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-resourcedetector-gcp==1.6.0a0"}}, "grpc_python_dependencies_39_opentelemetry_sdk": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-sdk==1.25.0"}}, "grpc_python_dependencies_39_opentelemetry_semantic_conventions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "opentelemetry-semantic-conventions==0.46b0"}}, "grpc_python_dependencies_39_prometheus_client": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "prometheus_client==0.20.0"}}, "grpc_python_dependencies_39_proto_plus": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "proto-plus==1.22.3"}}, "grpc_python_dependencies_39_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "protobuf>=5.27.1,<6.0dev"}}, "grpc_python_dependencies_39_pyasn1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "pyasn1==0.5.0"}}, "grpc_python_dependencies_39_pyasn1_modules": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "pyasn1-modules==0.3.0"}}, "grpc_python_dependencies_39_requests": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "requests==2.25.1"}}, "grpc_python_dependencies_39_rsa": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "rsa==4.9"}}, "grpc_python_dependencies_39_setuptools": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "setuptools==44.1.1"}}, "grpc_python_dependencies_39_typing_extensions": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "typing-extensions==4.9.0"}}, "grpc_python_dependencies_39_urllib3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "urllib3==1.26.18"}}, "grpc_python_dependencies_39_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "wheel==0.38.1"}}, "grpc_python_dependencies_39_wrapt": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "wrapt==1.16.0"}}, "grpc_python_dependencies_39_zipp": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zipp==3.17.0"}}, "grpc_python_dependencies_39_zope_event": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zope.event==4.5.0"}}, "grpc_python_dependencies_39_zope_interface": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@grpc_python_dependencies//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "grpc_python_dependencies_39", "requirement": "zope.interface==6.1"}}, "pgv_pip_deps_310_astunparse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "astunparse==1.6.3"}}, "pgv_pip_deps_310_jinja2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "jinja2==3.1.3"}}, "pgv_pip_deps_310_markupsafe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "markupsafe==2.1.5"}}, "pgv_pip_deps_310_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "protobuf==5.26.0"}}, "pgv_pip_deps_310_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "six==1.16.0"}}, "pgv_pip_deps_310_validate_email": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "validate-email==1.3"}}, "pgv_pip_deps_310_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_10_host//:python", "repo": "pgv_pip_deps_310", "requirement": "wheel==0.43.0"}}, "pgv_pip_deps_311_astunparse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "astunparse==1.6.3"}}, "pgv_pip_deps_311_jinja2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "jinja2==3.1.3"}}, "pgv_pip_deps_311_markupsafe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "markupsafe==2.1.5"}}, "pgv_pip_deps_311_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "protobuf==5.26.0"}}, "pgv_pip_deps_311_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "six==1.16.0"}}, "pgv_pip_deps_311_validate_email": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "validate-email==1.3"}}, "pgv_pip_deps_311_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "pgv_pip_deps_311", "requirement": "wheel==0.43.0"}}, "pgv_pip_deps_312_astunparse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "astunparse==1.6.3"}}, "pgv_pip_deps_312_jinja2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "jinja2==3.1.3"}}, "pgv_pip_deps_312_markupsafe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "markupsafe==2.1.5"}}, "pgv_pip_deps_312_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "protobuf==5.26.0"}}, "pgv_pip_deps_312_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "six==1.16.0"}}, "pgv_pip_deps_312_validate_email": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "validate-email==1.3"}}, "pgv_pip_deps_312_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_12_host//:python", "repo": "pgv_pip_deps_312", "requirement": "wheel==0.43.0"}}, "pgv_pip_deps_313_astunparse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "astunparse==1.6.3"}}, "pgv_pip_deps_313_jinja2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "jinja2==3.1.3"}}, "pgv_pip_deps_313_markupsafe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "markupsafe==2.1.5"}}, "pgv_pip_deps_313_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "protobuf==5.26.0"}}, "pgv_pip_deps_313_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "six==1.16.0"}}, "pgv_pip_deps_313_validate_email": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "validate-email==1.3"}}, "pgv_pip_deps_313_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_13_host//:python", "repo": "pgv_pip_deps_313", "requirement": "wheel==0.43.0"}}, "pgv_pip_deps_39_astunparse": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "astunparse==1.6.3"}}, "pgv_pip_deps_39_jinja2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "jinja2==3.1.3"}}, "pgv_pip_deps_39_markupsafe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "markupsafe==2.1.5"}}, "pgv_pip_deps_39_protobuf": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "protobuf==5.26.0"}}, "pgv_pip_deps_39_six": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "six==1.16.0"}}, "pgv_pip_deps_39_validate_email": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "validate-email==1.3"}}, "pgv_pip_deps_39_wheel": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@pgv_pip_deps//{name}:{target}", "python_interpreter_target": "@@rules_python++python+python_3_9_host//:python", "repo": "pgv_pip_deps_39", "requirement": "wheel==0.43.0"}}, "rules_python_publish_deps_311_backports_tarfile_py3_none_any_77e284d7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "backports.tarfile-1.2.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "backports-tarfile==1.2.0", "sha256": "77e284d754527b01fb1e6fa8a1afe577858ebe4e9dad8919e34c862cb399bc34", "urls": ["https://files.pythonhosted.org/packages/b9/fa/123043af240e49752f1c4bd24da5053b6bd00cad78c2be53c0d1e8b975bc/backports.tarfile-1.2.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_backports_tarfile_sdist_d75e02c2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "backports_tarfile-1.2.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "backports-tarfile==1.2.0", "sha256": "d75e02c268746e1b8144c278978b6e98e85de6ad16f8e4b0844a154557eca991", "urls": ["https://files.pythonhosted.org/packages/86/72/cd9b395f25e290e633655a100af28cb253e4393396264a98bd5f5951d50f/backports_tarfile-1.2.0.tar.gz"]}}, "rules_python_publish_deps_311_certifi_py3_none_any_922820b5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "certifi-2024.8.30-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "certifi==2024.8.30", "sha256": "922820b53db7a7257ffbda3f597266d435245903d80737e34f8a45ff3e3230d8", "urls": ["https://files.pythonhosted.org/packages/12/90/3c9ff0512038035f59d279fddeb79f5f1eccd8859f06d6163c58798b9487/certifi-2024.8.30-py3-none-any.whl"]}}, "rules_python_publish_deps_311_certifi_sdist_bec941d2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "certifi-2024.8.30.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "certifi==2024.8.30", "sha256": "bec941d2aa8195e248a60b31ff9f0558284cf01a52591ceda73ea9afffd69fd9", "urls": ["https://files.pythonhosted.org/packages/b0/ee/9b19140fe824b367c04c5e1b369942dd754c4c5462d5674002f75c4dedc1/certifi-2024.8.30.tar.gz"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_aarch64_a1ed2dd2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a1ed2dd2972641495a3ec98445e09766f077aee98a1c896dcb4ad0d303628e41", "urls": ["https://files.pythonhosted.org/packages/2e/ea/70ce63780f096e16ce8588efe039d3c4f91deb1dc01e9c73a287939c79a6/cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_ppc64le_46bf4316": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "46bf43160c1a35f7ec506d254e5c890f3c03648a4dbac12d624e4490a7046cd1", "urls": ["https://files.pythonhosted.org/packages/1c/a0/a4fa9f4f781bda074c3ddd57a572b060fa0df7655d2a4247bbe277200146/cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_s390x_a24ed04c": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a24ed04c8ffd54b0729c07cee15a81d964e6fee0e3d4d342a27b020d22959dc6", "urls": ["https://files.pythonhosted.org/packages/62/12/ce8710b5b8affbcdd5c6e367217c242524ad17a02fe5beec3ee339f69f85/cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_x86_64_610faea7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "610faea79c43e44c71e1ec53a554553fa22321b65fae24889706c0a84d4ad86d", "urls": ["https://files.pythonhosted.org/packages/ff/6b/d45873c5e0242196f042d555526f92aa9e0c32355a1be1ff8c27f077fd37/cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_aarch64_a9b15d49": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "a9b15d491f3ad5d692e11f6b71f7857e7835eb677955c00cc0aefcd0669adaf6", "urls": ["https://files.pythonhosted.org/packages/1a/52/d9a0e523a572fbccf2955f5abe883cfa8bcc570d7faeee06336fbd50c9fc/cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl"]}}, "rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_x86_64_fc48c783": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "fc48c783f9c87e60831201f2cce7f3b2e4846bf4d8728eabe54d60700b318a0b", "urls": ["https://files.pythonhosted.org/packages/f8/4a/34599cac7dfcd888ff54e801afe06a19c17787dfd94495ab0c8d35fe99fb/cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl"]}}, "rules_python_publish_deps_311_cffi_sdist_1c39c601": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "cffi-1.17.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cffi==1.17.1", "sha256": "1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824", "urls": ["https://files.pythonhosted.org/packages/fc/97/c783634659c2920c3fc70419e3af40972dbaf758daa229a7d6ea6135c90d/cffi-1.17.1.tar.gz"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_universal2_0d99dd8f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "0d99dd8ff461990f12d6e42c7347fd9ab2532fb70e9621ba520f9e8637161d7c", "urls": ["https://files.pythonhosted.org/packages/9c/61/73589dcc7a719582bf56aae309b6103d2762b526bffe189d635a7fcfd998/charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_x86_64_c57516e5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "c57516e58fd17d03ebe67e181a4e4e2ccab1168f8c2976c6a334d4f819fe5944", "urls": ["https://files.pythonhosted.org/packages/77/d5/8c982d58144de49f59571f940e329ad6e8615e1e82ef84584c5eeb5e1d72/charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_11_0_arm64_6dba5d19": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "6dba5d19c4dfab08e58d5b36304b3f92f3bd5d42c1a3fa37b5ba5cdf6dfcbcee", "urls": ["https://files.pythonhosted.org/packages/bf/19/411a64f01ee971bed3231111b69eb56f9331a769072de479eae7de52296d/charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_aarch64_bf4475b8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "bf4475b82be41b07cc5e5ff94810e6a01f276e37c2d55571e3fe175e467a1a1c", "urls": ["https://files.pythonhosted.org/packages/4c/92/97509850f0d00e9f14a46bc751daabd0ad7765cff29cdfb66c68b6dad57f/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_ppc64le_ce031db0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "ce031db0408e487fd2775d745ce30a7cd2923667cf3b69d48d219f1d8f5ddeb6", "urls": ["https://files.pythonhosted.org/packages/e2/29/d227805bff72ed6d6cb1ce08eec707f7cfbd9868044893617eb331f16295/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_s390x_8ff4e7cd": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "8ff4e7cdfdb1ab5698e675ca622e72d58a6fa2a8aa58195de0c0061288e6e3ea", "urls": ["https://files.pythonhosted.org/packages/13/bc/87c2c9f2c144bedfa62f894c3007cd4530ba4b5351acb10dc786428a50f0/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_x86_64_3710a975": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "3710a9751938947e6327ea9f3ea6332a09bf0ba0c09cae9cb1f250bd1f1549bc", "urls": ["https://files.pythonhosted.org/packages/eb/5b/6f10bad0f6461fa272bfbbdf5d0023b5fb9bc6217c92bf068fa5a99820f5/charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_aarch64_47334db7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "47334db71978b23ebcf3c0f9f5ee98b8d65992b65c9c4f2d34c2eaf5bcaf0594", "urls": ["https://files.pythonhosted.org/packages/d7/a1/493919799446464ed0299c8eef3c3fad0daf1c3cd48bff9263c731b0d9e2/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_ppc64le_f1a2f519": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "f1a2f519ae173b5b6a2c9d5fa3116ce16e48b3462c8b96dfdded11055e3d6365", "urls": ["https://files.pythonhosted.org/packages/75/d2/0ab54463d3410709c09266dfb416d032a08f97fd7d60e94b8c6ef54ae14b/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_s390x_63bc5c4a": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "63bc5c4ae26e4bc6be6469943b8253c0fd4e4186c43ad46e713ea61a0ba49129", "urls": ["https://files.pythonhosted.org/packages/8d/c9/27e41d481557be53d51e60750b85aa40eaf52b841946b3cdeff363105737/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_x86_64_bcb4f8ea": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "bcb4f8ea87d03bc51ad04add8ceaf9b0f085ac045ab4d74e73bbc2dc033f0236", "urls": ["https://files.pythonhosted.org/packages/ee/44/4f62042ca8cdc0cabf87c0fc00ae27cd8b53ab68be3605ba6d071f742ad3/charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_cp311_cp311_win_amd64_cee4373f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "cee4373f4d3ad28f1ab6290684d8e2ebdb9e7a1b74fdc39e4c211995f77bec27", "urls": ["https://files.pythonhosted.org/packages/0b/6e/b13bd47fa9023b3699e94abf565b5a2f0b0be6e9ddac9812182596ee62e4/charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_py3_none_any_fe9f97fe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "charset_normalizer-3.4.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "fe9f97feb71aa9896b81973a7bbada8c49501dc73e58a10fcef6663af95e5079", "urls": ["https://files.pythonhosted.org/packages/bf/9b/08c0432272d77b04803958a4598a51e2a4b51c06640af8b8f0f908c18bf2/charset_normalizer-3.4.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_charset_normalizer_sdist_223217c3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "charset_normalizer-3.4.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "charset-normalizer==3.4.0", "sha256": "223217c3d4f82c3ac5e29032b3f1c2eb0fb591b72161f86d93f5719079dae93e", "urls": ["https://files.pythonhosted.org/packages/f2/4f/e1808dc01273379acc506d18f1504eb2d299bd4131743b9fc54d7be4df1e/charset_normalizer-3.4.0.tar.gz"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_aarch64_846da004": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "846da004a5804145a5f441b8530b4bf35afbf7da70f82409f151695b127213d5", "urls": ["https://files.pythonhosted.org/packages/2f/78/55356eb9075d0be6e81b59f45c7b48df87f76a20e73893872170471f3ee8/cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_x86_64_0f996e72": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "0f996e7268af62598f2fc1204afa98a3b5712313a55c4c9d434aef49cadc91d4", "urls": ["https://files.pythonhosted.org/packages/2a/2c/488776a3dc843f95f86d2f957ca0fc3407d0242b50bede7fad1e339be03f/cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_aarch64_f7b178f1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "f7b178f11ed3664fd0e995a47ed2b5ff0a12d893e41dd0494f406d1cf555cab7", "urls": ["https://files.pythonhosted.org/packages/7c/04/2345ca92f7a22f601a9c62961741ef7dd0127c39f7310dffa0041c80f16f/cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_x86_64_c2e6fc39": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "c2e6fc39c4ab499049df3bdf567f768a723a5e8464816e8f009f121a5a9f4405", "urls": ["https://files.pythonhosted.org/packages/ac/25/e715fa0bc24ac2114ed69da33adf451a38abb6f3f24ec207908112e9ba53/cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_aarch64_e1be4655": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "e1be4655c7ef6e1bbe6b5d0403526601323420bcf414598955968c9ef3eb7d16", "urls": ["https://files.pythonhosted.org/packages/21/ce/b9c9ff56c7164d8e2edfb6c9305045fbc0df4508ccfdb13ee66eb8c95b0e/cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_x86_64_df6b6c6d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "df6b6c6d742395dd77a23ea3728ab62f98379eff8fb61be2744d4679ab678f73", "urls": ["https://files.pythonhosted.org/packages/2a/33/b3682992ab2e9476b9c81fff22f02c8b0a1e6e1d49ee1750a67d85fd7ed2/cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_cryptography_sdist_315b9001": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "cryptography-43.0.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "cryptography==43.0.3", "sha256": "315b9001266a492a6ff443b61238f956b214dbec9910a081ba5b6646a055a805", "urls": ["https://files.pythonhosted.org/packages/0d/05/07b55d1fa21ac18c3a8c79f764e2514e6f6a9698f1be44994f5adf0d29db/cryptography-43.0.3.tar.gz"]}}, "rules_python_publish_deps_311_docutils_py3_none_any_dafca5b9": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "docutils-0.21.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "docutils==0.21.2", "sha256": "dafca5b9e384f0e419294eb4d2ff9fa826435bf15f15b7bd45723e8ad76811b2", "urls": ["https://files.pythonhosted.org/packages/8f/d7/9322c609343d929e75e7e5e6255e614fcc67572cfd083959cdef3b7aad79/docutils-0.21.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_docutils_sdist_3a6b1873": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "docutils-0.21.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "docutils==0.21.2", "sha256": "3a6b18732edf182daa3cd12775bbb338cf5691468f91eeeb109deff6ebfa986f", "urls": ["https://files.pythonhosted.org/packages/ae/ed/aefcc8cd0ba62a0560c3c18c33925362d46c6075480bfa4df87b28e169a9/docutils-0.21.2.tar.gz"]}}, "rules_python_publish_deps_311_idna_py3_none_any_946d195a": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "idna-3.10-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "idna==3.10", "sha256": "946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", "urls": ["https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl"]}}, "rules_python_publish_deps_311_idna_sdist_12f65c9b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "idna-3.10.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "idna==3.10", "sha256": "12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", "urls": ["https://files.pythonhosted.org/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz"]}}, "rules_python_publish_deps_311_importlib_metadata_py3_none_any_45e54197": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "importlib_metadata-8.5.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "importlib-metadata==8.5.0", "sha256": "45e54197d28b7a7f1559e60b95e7c567032b602131fbd588f1497f47880aa68b", "urls": ["https://files.pythonhosted.org/packages/a0/d9/a1e041c5e7caa9a05c925f4bdbdfb7f006d1f74996af53467bc394c97be7/importlib_metadata-8.5.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_importlib_metadata_sdist_71522656": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "importlib_metadata-8.5.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "importlib-metadata==8.5.0", "sha256": "71522656f0abace1d072b9e5481a48f07c138e00f079c38c8f883823f9c26bd7", "urls": ["https://files.pythonhosted.org/packages/cd/12/33e59336dca5be0c398a7482335911a33aa0e20776128f038019f1a95f1b/importlib_metadata-8.5.0.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_classes_py3_none_any_f662826b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.classes-3.4.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-classes==3.4.0", "sha256": "f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790", "urls": ["https://files.pythonhosted.org/packages/7f/66/b15ce62552d84bbfcec9a4873ab79d993a1dd4edb922cbfccae192bd5b5f/jaraco.classes-3.4.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_classes_sdist_47a024b5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco.classes-3.4.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-classes==3.4.0", "sha256": "47a024b51d0239c0dd8c8540c6c7f484be3b8fcf0b2d85c13825780d3b3f3acd", "urls": ["https://files.pythonhosted.org/packages/06/c0/ed4a27bc5571b99e3cff68f8a9fa5b56ff7df1c2251cc715a652ddd26402/jaraco.classes-3.4.0.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_context_py3_none_any_f797fc48": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.context-6.0.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-context==6.0.1", "sha256": "f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4", "urls": ["https://files.pythonhosted.org/packages/ff/db/0c52c4cf5e4bd9f5d7135ec7669a3a767af21b3a308e1ed3674881e52b62/jaraco.context-6.0.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_context_sdist_9bae4ea5": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco_context-6.0.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-context==6.0.1", "sha256": "9bae4ea555cf0b14938dc0aee7c9f32ed303aa20a3b73e7dc80111628792d1b3", "urls": ["https://files.pythonhosted.org/packages/df/ad/f3777b81bf0b6e7bc7514a1656d3e637b2e8e15fab2ce3235730b3e7a4e6/jaraco_context-6.0.1.tar.gz"]}}, "rules_python_publish_deps_311_jaraco_functools_py3_none_any_ad159f13": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "jaraco.functools-4.1.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-functools==4.1.0", "sha256": "ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649", "urls": ["https://files.pythonhosted.org/packages/9f/4f/24b319316142c44283d7540e76c7b5a6dbd5db623abd86bb7b3491c21018/jaraco.functools-4.1.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jaraco_functools_sdist_70f7e0e2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jaraco_functools-4.1.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jaraco-functools==4.1.0", "sha256": "70f7e0e2ae076498e212562325e805204fc092d7b4c17e0e86c959e249701a9d", "urls": ["https://files.pythonhosted.org/packages/ab/23/9894b3df5d0a6eb44611c36aec777823fc2e07740dabbd0b810e19594013/jaraco_functools-4.1.0.tar.gz"]}}, "rules_python_publish_deps_311_jeepney_py3_none_any_c0a454ad": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "jeepney-0.8.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jeepney==0.8.0", "sha256": "c0a454ad016ca575060802ee4d590dd912e35c122fa04e70306de3d076cce755", "urls": ["https://files.pythonhosted.org/packages/ae/72/2a1e2290f1ab1e06f71f3d0f1646c9e4634e70e1d37491535e19266e8dc9/jeepney-0.8.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_jeepney_sdist_5efe48d2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "jeepney-0.8.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "jeepney==0.8.0", "sha256": "5efe48d255973902f6badc3ce55e2aa6c5c3b3bc642059ef3a91247bcfcc5806", "urls": ["https://files.pythonhosted.org/packages/d6/f4/154cf374c2daf2020e05c3c6a03c91348d59b23c5366e968feb198306fdf/jeepney-0.8.0.tar.gz"]}}, "rules_python_publish_deps_311_keyring_py3_none_any_5426f817": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "keyring-25.4.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "keyring==25.4.1", "sha256": "5426f817cf7f6f007ba5ec722b1bcad95a75b27d780343772ad76b17cb47b0bf", "urls": ["https://files.pythonhosted.org/packages/83/25/e6d59e5f0a0508d0dca8bb98c7f7fd3772fc943ac3f53d5ab18a218d32c0/keyring-25.4.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_keyring_sdist_b07ebc55": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "keyring-25.4.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "keyring==25.4.1", "sha256": "b07ebc55f3e8ed86ac81dd31ef14e81ace9dd9c3d4b5d77a6e9a2016d0d71a1b", "urls": ["https://files.pythonhosted.org/packages/a5/1c/2bdbcfd5d59dc6274ffb175bc29aa07ecbfab196830e0cfbde7bd861a2ea/keyring-25.4.1.tar.gz"]}}, "rules_python_publish_deps_311_markdown_it_py_py3_none_any_35521684": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "markdown_it_py-3.0.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "markdown-it-py==3.0.0", "sha256": "355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", "urls": ["https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_markdown_it_py_sdist_e3f60a94": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "markdown-it-py-3.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "markdown-it-py==3.0.0", "sha256": "e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", "urls": ["https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz"]}}, "rules_python_publish_deps_311_mdurl_py3_none_any_84008a41": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "mdurl-0.1.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "mdurl==0.1.2", "sha256": "84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", "urls": ["https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_mdurl_sdist_bb413d29": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "mdurl-0.1.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "mdurl==0.1.2", "sha256": "bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", "urls": ["https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz"]}}, "rules_python_publish_deps_311_more_itertools_py3_none_any_037b0d32": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "more_itertools-10.5.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "more-itertools==10.5.0", "sha256": "037b0d3203ce90cca8ab1defbbdac29d5f993fc20131f3664dc8d6acfa872aef", "urls": ["https://files.pythonhosted.org/packages/48/7e/3a64597054a70f7c86eb0a7d4fc315b8c1ab932f64883a297bdffeb5f967/more_itertools-10.5.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_more_itertools_sdist_5482bfef": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "more-itertools-10.5.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "more-itertools==10.5.0", "sha256": "5482bfef7849c25dc3c6dd53a6173ae4795da2a41a80faea6700d9f5846c5da6", "urls": ["https://files.pythonhosted.org/packages/51/78/65922308c4248e0eb08ebcbe67c95d48615cc6f27854b6f2e57143e9178f/more-itertools-10.5.0.tar.gz"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_14c5a72e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "14c5a72e9fe82aea5fe3072116ad4661af5cf8e8ff8fc5ad3450f123e4925e86", "urls": ["https://files.pythonhosted.org/packages/b3/89/1daff5d9ba5a95a157c092c7c5f39b8dd2b1ddb4559966f808d31cfb67e0/nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_7b7c2a3c": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "7b7c2a3c9eb1a827d42539aa64091640bd275b81e097cd1d8d82ef91ffa2e811", "urls": ["https://files.pythonhosted.org/packages/2c/b6/42fc3c69cabf86b6b81e4c051a9b6e249c5ba9f8155590222c2622961f58/nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_aarch64_42c64511": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "42c64511469005058cd17cc1537578eac40ae9f7200bedcfd1fc1a05f4f8c200", "urls": ["https://files.pythonhosted.org/packages/45/b9/833f385403abaf0023c6547389ec7a7acf141ddd9d1f21573723a6eab39a/nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_armv7l_0411beb0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "0411beb0589eacb6734f28d5497ca2ed379eafab8ad8c84b31bb5c34072b7164", "urls": ["https://files.pythonhosted.org/packages/05/2b/85977d9e11713b5747595ee61f381bc820749daf83f07b90b6c9964cf932/nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64_5f36b271": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "5f36b271dae35c465ef5e9090e1fdaba4a60a56f0bb0ba03e0932a66f28b9189", "urls": ["https://files.pythonhosted.org/packages/72/f2/5c894d5265ab80a97c68ca36f25c8f6f0308abac649aaf152b74e7e854a8/nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64le_34c03fa7": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "34c03fa78e328c691f982b7c03d4423bdfd7da69cd707fe572f544cf74ac23ad", "urls": ["https://files.pythonhosted.org/packages/ab/a7/375afcc710dbe2d64cfbd69e31f82f3e423d43737258af01f6a56d844085/nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_s390x_19aaba96": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "19aaba96e0f795bd0a6c56291495ff59364f4300d4a39b29a0abc9cb3774a84b", "urls": ["https://files.pythonhosted.org/packages/c2/a8/3bb02d0c60a03ad3a112b76c46971e9480efa98a8946677b5a59f60130ca/nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_x86_64_de3ceed6": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "de3ceed6e661954871d6cd78b410213bdcb136f79aafe22aa7182e028b8c7307", "urls": ["https://files.pythonhosted.org/packages/1b/63/6ab90d0e5225ab9780f6c9fb52254fa36b52bb7c188df9201d05b647e5e1/nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_aarch64_f0eca9ca": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "f0eca9ca8628dbb4e916ae2491d72957fdd35f7a5d326b7032a345f111ac07fe", "urls": ["https://files.pythonhosted.org/packages/a3/da/0c4e282bc3cff4a0adf37005fa1fb42257673fbc1bbf7d1ff639ec3d255a/nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_armv7l_3a157ab1": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "3a157ab149e591bb638a55c8c6bcb8cdb559c8b12c13a8affaba6cedfe51713a", "urls": ["https://files.pythonhosted.org/packages/de/81/c291231463d21da5f8bba82c8167a6d6893cc5419b0639801ee5d3aeb8a9/nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_x86_64_36c95d4b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "36c95d4b70530b320b365659bb5034341316e6a9b30f0b25fa9c9eff4c27a204", "urls": ["https://files.pythonhosted.org/packages/eb/61/73a007c74c37895fdf66e0edcd881f5eaa17a348ff02f4bb4bc906d61085/nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl"]}}, "rules_python_publish_deps_311_nh3_cp37_abi3_win_amd64_8ce0f819": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "nh3-0.2.18-cp37-abi3-win_amd64.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "8ce0f819d2f1933953fca255db2471ad58184a60508f03e6285e5114b6254844", "urls": ["https://files.pythonhosted.org/packages/26/8d/53c5b19c4999bdc6ba95f246f4ef35ca83d7d7423e5e38be43ad66544e5d/nh3-0.2.18-cp37-abi3-win_amd64.whl"]}}, "rules_python_publish_deps_311_nh3_sdist_94a16692": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "nh3-0.2.18.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "nh3==0.2.18", "sha256": "94a166927e53972a9698af9542ace4e38b9de50c34352b962f4d9a7d4c927af4", "urls": ["https://files.pythonhosted.org/packages/62/73/10df50b42ddb547a907deeb2f3c9823022580a7a47281e8eae8e003a9639/nh3-0.2.18.tar.gz"]}}, "rules_python_publish_deps_311_pkginfo_py3_none_any_889a6da2": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "pkginfo-1.10.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pkginfo==1.10.0", "sha256": "889a6da2ed7ffc58ab5b900d888ddce90bce912f2d2de1dc1c26f4cb9fe65097", "urls": ["https://files.pythonhosted.org/packages/56/09/054aea9b7534a15ad38a363a2bd974c20646ab1582a387a95b8df1bfea1c/pkginfo-1.10.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pkginfo_sdist_5df73835": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pkginfo-1.10.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pkginfo==1.10.0", "sha256": "5df73835398d10db79f8eecd5cd86b1f6d29317589ea70796994d49399af6297", "urls": ["https://files.pythonhosted.org/packages/2f/72/347ec5be4adc85c182ed2823d8d1c7b51e13b9a6b0c1aae59582eca652df/pkginfo-1.10.0.tar.gz"]}}, "rules_python_publish_deps_311_pycparser_py3_none_any_c3702b6d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "pycparser-2.22-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pycparser==2.22", "sha256": "c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", "urls": ["https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pycparser_sdist_491c8be9": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pycparser-2.22.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pycparser==2.22", "sha256": "491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6", "urls": ["https://files.pythonhosted.org/packages/1d/b2/31537cf4b1ca988837256c910a668b553fceb8f069bedc4b1c826024b52c/pycparser-2.22.tar.gz"]}}, "rules_python_publish_deps_311_pygments_py3_none_any_b8e6aca0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "pygments-2.18.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pygments==2.18.0", "sha256": "b8e6aca0523f3ab76fee51799c488e38782ac06eafcf95e7ba832985c8e7b13a", "urls": ["https://files.pythonhosted.org/packages/f7/3f/01c8b82017c199075f8f788d0d906b9ffbbc5a47dc9918a945e13d5a2bda/pygments-2.18.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pygments_sdist_786ff802": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pygments-2.18.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pygments==2.18.0", "sha256": "786ff802f32e91311bff3889f6e9a86e81505fe99f2735bb6d60ae0c5004f199", "urls": ["https://files.pythonhosted.org/packages/8e/62/8336eff65bcbc8e4cb5d05b55faf041285951b6e80f33e2bff2024788f31/pygments-2.18.0.tar.gz"]}}, "rules_python_publish_deps_311_pywin32_ctypes_py3_none_any_8a151337": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_windows_x86_64"], "filename": "pywin32_ctypes-0.2.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pywin32-ctypes==0.2.3", "sha256": "8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8", "urls": ["https://files.pythonhosted.org/packages/de/3d/8161f7711c017e01ac9f008dfddd9410dff3674334c233bde66e7ba65bbf/pywin32_ctypes-0.2.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_pywin32_ctypes_sdist_d162dc04": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "pywin32-ctypes-0.2.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "pywin32-ctypes==0.2.3", "sha256": "d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755", "urls": ["https://files.pythonhosted.org/packages/85/9f/01a1a99704853cb63f253eea009390c88e7131c67e66a0a02099a8c917cb/pywin32-ctypes-0.2.3.tar.gz"]}}, "rules_python_publish_deps_311_readme_renderer_py3_none_any_2fbca89b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "readme_renderer-44.0-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "readme-renderer==44.0", "sha256": "2fbca89b81a08526aadf1357a8c2ae889ec05fb03f5da67f9769c9a592166151", "urls": ["https://files.pythonhosted.org/packages/e1/67/921ec3024056483db83953ae8e48079ad62b92db7880013ca77632921dd0/readme_renderer-44.0-py3-none-any.whl"]}}, "rules_python_publish_deps_311_readme_renderer_sdist_8712034e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "readme_renderer-44.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "readme-renderer==44.0", "sha256": "8712034eabbfa6805cacf1402b4eeb2a73028f72d1166d6f5cb7f9c047c5d1e1", "urls": ["https://files.pythonhosted.org/packages/5a/a9/104ec9234c8448c4379768221ea6df01260cd6c2ce13182d4eac531c8342/readme_renderer-44.0.tar.gz"]}}, "rules_python_publish_deps_311_requests_py3_none_any_70761cfe": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "requests-2.32.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests==2.32.3", "sha256": "70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6", "urls": ["https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_requests_sdist_55365417": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "requests-2.32.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests==2.32.3", "sha256": "55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760", "urls": ["https://files.pythonhosted.org/packages/63/70/2bf7780ad2d390a8d301ad0b550f1581eadbd9a20f896afe06353c2a2913/requests-2.32.3.tar.gz"]}}, "rules_python_publish_deps_311_requests_toolbelt_py2_none_any_cccfdd66": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "requests_toolbelt-1.0.0-py2.py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests-toolbelt==1.0.0", "sha256": "cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06", "urls": ["https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl"]}}, "rules_python_publish_deps_311_requests_toolbelt_sdist_7681a0a3": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "requests-toolbelt-1.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "requests-toolbelt==1.0.0", "sha256": "7681a0a3d047012b5bdc0ee37d7f8f07ebe76ab08caeccfc3921ce23c88d5bc6", "urls": ["https://files.pythonhosted.org/packages/f3/61/d7545dafb7ac2230c70d38d31cbfe4cc64f7144dc41f6e4e4b78ecd9f5bb/requests-toolbelt-1.0.0.tar.gz"]}}, "rules_python_publish_deps_311_rfc3986_py2_none_any_50b1502b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "rfc3986-2.0.0-py2.py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rfc3986==2.0.0", "sha256": "50b1502b60e289cb37883f3dfd34532b8873c7de9f49bb546641ce9cbd256ebd", "urls": ["https://files.pythonhosted.org/packages/ff/9a/9afaade874b2fa6c752c36f1548f718b5b83af81ed9b76628329dab81c1b/rfc3986-2.0.0-py2.py3-none-any.whl"]}}, "rules_python_publish_deps_311_rfc3986_sdist_97aacf9d": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "rfc3986-2.0.0.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rfc3986==2.0.0", "sha256": "97aacf9dbd4bfd829baad6e6309fa6573aaf1be3f6fa735c8ab05e46cecb261c", "urls": ["https://files.pythonhosted.org/packages/85/40/1520d68bfa07ab5a6f065a186815fb6610c86fe957bc065754e47f7b0840/rfc3986-2.0.0.tar.gz"]}}, "rules_python_publish_deps_311_rich_py3_none_any_9836f509": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "rich-13.9.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rich==13.9.3", "sha256": "9836f5096eb2172c9e77df411c1b009bace4193d6a481d534fea75ebba758283", "urls": ["https://files.pythonhosted.org/packages/9a/e2/10e9819cf4a20bd8ea2f5dabafc2e6bf4a78d6a0965daeb60a4b34d1c11f/rich-13.9.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_rich_sdist_bc1e01b8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "rich-13.9.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "rich==13.9.3", "sha256": "bc1e01b899537598cf02579d2b9f4a415104d3fc439313a7a2c165d76557a08e", "urls": ["https://files.pythonhosted.org/packages/d9/e9/cf9ef5245d835065e6673781dbd4b8911d352fb770d56cf0879cf11b7ee1/rich-13.9.3.tar.gz"]}}, "rules_python_publish_deps_311_secretstorage_py3_none_any_f356e662": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "filename": "SecretStorage-3.3.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "secretstorage==3.3.3", "sha256": "f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99", "urls": ["https://files.pythonhosted.org/packages/54/24/b4293291fa1dd830f353d2cb163295742fa87f179fcc8a20a306a81978b7/SecretStorage-3.3.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_secretstorage_sdist_2403533e": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "SecretStorage-3.3.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "secretstorage==3.3.3", "sha256": "2403533ef369eca6d2ba81718576c5e0f564d5cca1b58f73a8b23e7d4eeebd77", "urls": ["https://files.pythonhosted.org/packages/53/a4/f48c9d79cb507ed1373477dbceaba7401fd8a23af63b837fa61f1dcd3691/SecretStorage-3.3.3.tar.gz"]}}, "rules_python_publish_deps_311_twine_py3_none_any_215dbe7b": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "twine-5.1.1-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "twine==5.1.1", "sha256": "215dbe7b4b94c2c50a7315c0275d2258399280fbb7d04182c7e55e24b5f93997", "urls": ["https://files.pythonhosted.org/packages/5d/ec/00f9d5fd040ae29867355e559a94e9a8429225a0284a3f5f091a3878bfc0/twine-5.1.1-py3-none-any.whl"]}}, "rules_python_publish_deps_311_twine_sdist_9aa08251": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "twine-5.1.1.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "twine==5.1.1", "sha256": "9aa0825139c02b3434d913545c7b847a21c835e11597f5255842d457da2322db", "urls": ["https://files.pythonhosted.org/packages/77/68/bd982e5e949ef8334e6f7dcf76ae40922a8750aa2e347291ae1477a4782b/twine-5.1.1.tar.gz"]}}, "rules_python_publish_deps_311_urllib3_py3_none_any_ca899ca0": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "urllib3-2.2.3-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "urllib3==2.2.3", "sha256": "ca899ca043dcb1bafa3e262d73aa25c465bfb49e0bd9dd5d59f1d0acba2f8fac", "urls": ["https://files.pythonhosted.org/packages/ce/d9/5f4c13cecde62396b0d3fe530a50ccea91e7dfc1ccf0e09c228841bb5ba8/urllib3-2.2.3-py3-none-any.whl"]}}, "rules_python_publish_deps_311_urllib3_sdist_e7d814a8": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "urllib3-2.2.3.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "urllib3==2.2.3", "sha256": "e7d814a81dad81e6caf2ec9fdedb284ecc9c73076b62654547cc64ccdcae26e9", "urls": ["https://files.pythonhosted.org/packages/ed/63/22ba4ebfe7430b76388e7cd448d5478814d3032121827c12a2cc287e2260/urllib3-2.2.3.tar.gz"]}}, "rules_python_publish_deps_311_zipp_py3_none_any_a817ac80": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "filename": "zipp-3.20.2-py3-none-any.whl", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "zipp==3.20.2", "sha256": "a817ac80d6cf4b23bf7f2828b7cabf326f15a001bea8b1f9b49631780ba28350", "urls": ["https://files.pythonhosted.org/packages/62/8b/5ba542fa83c90e09eac972fc9baca7a88e7e7ca4b221a89251954019308b/zipp-3.20.2-py3-none-any.whl"]}}, "rules_python_publish_deps_311_zipp_sdist_bc9eb26f": {"repoRuleId": "@@rules_python+//python/private/pypi:whl_library.bzl%whl_library", "attributes": {"dep_template": "@rules_python_publish_deps//{name}:{target}", "experimental_target_platforms": ["cp311_linux_aarch64", "cp311_linux_arm", "cp311_linux_ppc", "cp311_linux_s390x", "cp311_linux_x86_64", "cp311_osx_aarch64", "cp311_osx_x86_64", "cp311_windows_x86_64"], "extra_pip_args": ["--index-url", "https://pypi.org/simple"], "filename": "zipp-3.20.2.tar.gz", "python_interpreter_target": "@@rules_python++python+python_3_11_host//:python", "repo": "rules_python_publish_deps_311", "requirement": "zipp==3.20.2", "sha256": "bc9eb26f4506fda01b81bcde0ca78103b6e62f991b381fec825435c836edbc29", "urls": ["https://files.pythonhosted.org/packages/54/bf/5c0000c44ebc80123ecbdddba1f5dcd94a5ada602a9c225d84b5aaa55e86/zipp-3.20.2.tar.gz"]}}, "grpc_python_dependencies": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "grpc_python_dependencies", "extra_hub_aliases": {}, "whl_map": {"absl_py": "{\"grpc_python_dependencies_310_absl_py\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_absl_py\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_absl_py\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_absl_py\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_absl_py\":[{\"version\":\"3.9\"}]}", "cachetools": "{\"grpc_python_dependencies_310_cachetools\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_cachetools\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_cachetools\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_cachetools\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_cachetools\":[{\"version\":\"3.9\"}]}", "certifi": "{\"grpc_python_dependencies_310_certifi\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_certifi\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_certifi\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_certifi\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_certifi\":[{\"version\":\"3.9\"}]}", "chardet": "{\"grpc_python_dependencies_310_chardet\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_chardet\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_chardet\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_chardet\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_chardet\":[{\"version\":\"3.9\"}]}", "charset_normalizer": "{\"grpc_python_dependencies_310_charset_normalizer\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_charset_normalizer\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_charset_normalizer\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_charset_normalizer\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_charset_normalizer\":[{\"version\":\"3.9\"}]}", "coverage": "{\"grpc_python_dependencies_310_coverage\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_coverage\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_coverage\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_coverage\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_coverage\":[{\"version\":\"3.9\"}]}", "cython": "{\"grpc_python_dependencies_310_cython\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_cython\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_cython\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_cython\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_cython\":[{\"version\":\"3.9\"}]}", "deprecated": "{\"grpc_python_dependencies_310_deprecated\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_deprecated\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_deprecated\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_deprecated\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_deprecated\":[{\"version\":\"3.9\"}]}", "gevent": "{\"grpc_python_dependencies_310_gevent\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_gevent\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_gevent\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_gevent\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_gevent\":[{\"version\":\"3.9\"}]}", "google_api_core": "{\"grpc_python_dependencies_310_google_api_core\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_api_core\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_api_core\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_google_api_core\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_google_api_core\":[{\"version\":\"3.9\"}]}", "google_auth": "{\"grpc_python_dependencies_310_google_auth\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_auth\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_auth\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_google_auth\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_google_auth\":[{\"version\":\"3.9\"}]}", "google_cloud_monitoring": "{\"grpc_python_dependencies_310_google_cloud_monitoring\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_cloud_monitoring\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_cloud_monitoring\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_google_cloud_monitoring\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_google_cloud_monitoring\":[{\"version\":\"3.9\"}]}", "google_cloud_trace": "{\"grpc_python_dependencies_310_google_cloud_trace\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_google_cloud_trace\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_google_cloud_trace\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_google_cloud_trace\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_google_cloud_trace\":[{\"version\":\"3.9\"}]}", "googleapis_common_protos": "{\"grpc_python_dependencies_310_googleapis_common_protos\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_googleapis_common_protos\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_googleapis_common_protos\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_googleapis_common_protos\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_googleapis_common_protos\":[{\"version\":\"3.9\"}]}", "greenlet": "{\"grpc_python_dependencies_310_greenlet\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_greenlet\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_greenlet\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_greenlet\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_greenlet\":[{\"version\":\"3.9\"}]}", "idna": "{\"grpc_python_dependencies_310_idna\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_idna\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_idna\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_idna\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_idna\":[{\"version\":\"3.9\"}]}", "importlib_metadata": "{\"grpc_python_dependencies_310_importlib_metadata\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_importlib_metadata\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_importlib_metadata\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_importlib_metadata\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_importlib_metadata\":[{\"version\":\"3.9\"}]}", "oauth2client": "{\"grpc_python_dependencies_310_oauth2client\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_oauth2client\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_oauth2client\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_oauth2client\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_oauth2client\":[{\"version\":\"3.9\"}]}", "opencensus_context": "{\"grpc_python_dependencies_310_opencensus_context\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opencensus_context\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opencensus_context\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opencensus_context\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opencensus_context\":[{\"version\":\"3.9\"}]}", "opentelemetry_api": "{\"grpc_python_dependencies_310_opentelemetry_api\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_api\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_api\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opentelemetry_api\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opentelemetry_api\":[{\"version\":\"3.9\"}]}", "opentelemetry_exporter_prometheus": "{\"grpc_python_dependencies_310_opentelemetry_exporter_prometheus\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_exporter_prometheus\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_exporter_prometheus\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opentelemetry_exporter_prometheus\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opentelemetry_exporter_prometheus\":[{\"version\":\"3.9\"}]}", "opentelemetry_resourcedetector_gcp": "{\"grpc_python_dependencies_310_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opentelemetry_resourcedetector_gcp\":[{\"version\":\"3.9\"}]}", "opentelemetry_sdk": "{\"grpc_python_dependencies_310_opentelemetry_sdk\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_sdk\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_sdk\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opentelemetry_sdk\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opentelemetry_sdk\":[{\"version\":\"3.9\"}]}", "opentelemetry_semantic_conventions": "{\"grpc_python_dependencies_310_opentelemetry_semantic_conventions\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_opentelemetry_semantic_conventions\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_opentelemetry_semantic_conventions\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_opentelemetry_semantic_conventions\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_opentelemetry_semantic_conventions\":[{\"version\":\"3.9\"}]}", "prometheus_client": "{\"grpc_python_dependencies_310_prometheus_client\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_prometheus_client\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_prometheus_client\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_prometheus_client\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_prometheus_client\":[{\"version\":\"3.9\"}]}", "proto_plus": "{\"grpc_python_dependencies_310_proto_plus\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_proto_plus\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_proto_plus\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_proto_plus\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_proto_plus\":[{\"version\":\"3.9\"}]}", "protobuf": "{\"grpc_python_dependencies_310_protobuf\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_protobuf\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_protobuf\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_protobuf\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_protobuf\":[{\"version\":\"3.9\"}]}", "pyasn1": "{\"grpc_python_dependencies_310_pyasn1\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_pyasn1\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_pyasn1\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_pyasn1\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_pyasn1\":[{\"version\":\"3.9\"}]}", "pyasn1_modules": "{\"grpc_python_dependencies_310_pyasn1_modules\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_pyasn1_modules\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_pyasn1_modules\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_pyasn1_modules\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_pyasn1_modules\":[{\"version\":\"3.9\"}]}", "requests": "{\"grpc_python_dependencies_310_requests\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_requests\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_requests\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_requests\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_requests\":[{\"version\":\"3.9\"}]}", "rsa": "{\"grpc_python_dependencies_310_rsa\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_rsa\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_rsa\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_rsa\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_rsa\":[{\"version\":\"3.9\"}]}", "setuptools": "{\"grpc_python_dependencies_310_setuptools\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_setuptools\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_setuptools\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_setuptools\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_setuptools\":[{\"version\":\"3.9\"}]}", "typing_extensions": "{\"grpc_python_dependencies_310_typing_extensions\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_typing_extensions\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_typing_extensions\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_typing_extensions\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_typing_extensions\":[{\"version\":\"3.9\"}]}", "urllib3": "{\"grpc_python_dependencies_310_urllib3\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_urllib3\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_urllib3\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_urllib3\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_urllib3\":[{\"version\":\"3.9\"}]}", "wheel": "{\"grpc_python_dependencies_310_wheel\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_wheel\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_wheel\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_wheel\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_wheel\":[{\"version\":\"3.9\"}]}", "wrapt": "{\"grpc_python_dependencies_310_wrapt\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_wrapt\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_wrapt\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_wrapt\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_wrapt\":[{\"version\":\"3.9\"}]}", "zipp": "{\"grpc_python_dependencies_310_zipp\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zipp\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zipp\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_zipp\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_zipp\":[{\"version\":\"3.9\"}]}", "zope_event": "{\"grpc_python_dependencies_310_zope_event\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zope_event\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zope_event\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_zope_event\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_zope_event\":[{\"version\":\"3.9\"}]}", "zope_interface": "{\"grpc_python_dependencies_310_zope_interface\":[{\"version\":\"3.10\"}],\"grpc_python_dependencies_311_zope_interface\":[{\"version\":\"3.11\"}],\"grpc_python_dependencies_312_zope_interface\":[{\"version\":\"3.12\"}],\"grpc_python_dependencies_313_zope_interface\":[{\"version\":\"3.13\"}],\"grpc_python_dependencies_39_zope_interface\":[{\"version\":\"3.9\"}]}"}, "packages": ["absl_py", "cachetools", "certifi", "chardet", "charset_normalizer", "coverage", "cython", "deprecated", "gevent", "google_api_core", "google_auth", "google_cloud_monitoring", "google_cloud_trace", "googleapis_common_protos", "greenlet", "idna", "importlib_metadata", "oauth2client", "opencensus_context", "opentelemetry_api", "opentelemetry_exporter_prometheus", "opentelemetry_resourcedetector_gcp", "opentelemetry_sdk", "opentelemetry_semantic_conventions", "prometheus_client", "proto_plus", "protobuf", "pyasn1", "pyasn1_modules", "requests", "rsa", "setuptools", "typing_extensions", "urllib3", "wheel", "wrapt", "zipp", "zope_event", "zope_interface"], "groups": {}}}, "pgv_pip_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "pgv_pip_deps", "extra_hub_aliases": {}, "whl_map": {"astunparse": "{\"pgv_pip_deps_310_astunparse\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_astunparse\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_astunparse\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_astunparse\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_astunparse\":[{\"version\":\"3.9\"}]}", "jinja2": "{\"pgv_pip_deps_310_jinja2\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_jinja2\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_jinja2\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_jinja2\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_jinja2\":[{\"version\":\"3.9\"}]}", "markupsafe": "{\"pgv_pip_deps_310_markupsafe\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_markupsafe\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_markupsafe\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_markupsafe\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_markupsafe\":[{\"version\":\"3.9\"}]}", "protobuf": "{\"pgv_pip_deps_310_protobuf\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_protobuf\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_protobuf\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_protobuf\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_protobuf\":[{\"version\":\"3.9\"}]}", "six": "{\"pgv_pip_deps_310_six\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_six\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_six\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_six\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_six\":[{\"version\":\"3.9\"}]}", "validate_email": "{\"pgv_pip_deps_310_validate_email\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_validate_email\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_validate_email\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_validate_email\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_validate_email\":[{\"version\":\"3.9\"}]}", "wheel": "{\"pgv_pip_deps_310_wheel\":[{\"version\":\"3.10\"}],\"pgv_pip_deps_311_wheel\":[{\"version\":\"3.11\"}],\"pgv_pip_deps_312_wheel\":[{\"version\":\"3.12\"}],\"pgv_pip_deps_313_wheel\":[{\"version\":\"3.13\"}],\"pgv_pip_deps_39_wheel\":[{\"version\":\"3.9\"}]}"}, "packages": ["astunparse", "jinja2", "markupsafe", "protobuf", "six", "validate_email", "wheel"], "groups": {}}}, "rules_python_publish_deps": {"repoRuleId": "@@rules_python+//python/private/pypi:hub_repository.bzl%hub_repository", "attributes": {"repo_name": "rules_python_publish_deps", "extra_hub_aliases": {}, "whl_map": {"backports_tarfile": "{\"rules_python_publish_deps_311_backports_tarfile_py3_none_any_77e284d7\":[{\"filename\":\"backports.tarfile-1.2.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_backports_tarfile_sdist_d75e02c2\":[{\"filename\":\"backports_tarfile-1.2.0.tar.gz\",\"version\":\"3.11\"}]}", "certifi": "{\"rules_python_publish_deps_311_certifi_py3_none_any_922820b5\":[{\"filename\":\"certifi-2024.8.30-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_certifi_sdist_bec941d2\":[{\"filename\":\"certifi-2024.8.30.tar.gz\",\"version\":\"3.11\"}]}", "cffi": "{\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_aarch64_a1ed2dd2\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_ppc64le_46bf4316\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_s390x_a24ed04c\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_manylinux_2_17_x86_64_610faea7\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_aarch64_a9b15d49\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_cp311_cp311_musllinux_1_1_x86_64_fc48c783\":[{\"filename\":\"cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cffi_sdist_1c39c601\":[{\"filename\":\"cffi-1.17.1.tar.gz\",\"version\":\"3.11\"}]}", "charset_normalizer": "{\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_universal2_0d99dd8f\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_universal2.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_10_9_x86_64_c57516e5\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_10_9_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_macosx_11_0_arm64_6dba5d19\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-macosx_11_0_arm64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_aarch64_bf4475b8\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_ppc64le_ce031db0\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_s390x_8ff4e7cd\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_manylinux_2_17_x86_64_3710a975\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_aarch64_47334db7\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_ppc64le_f1a2f519\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_s390x_63bc5c4a\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_musllinux_1_2_x86_64_bcb4f8ea\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_cp311_cp311_win_amd64_cee4373f\":[{\"filename\":\"charset_normalizer-3.4.0-cp311-cp311-win_amd64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_py3_none_any_fe9f97fe\":[{\"filename\":\"charset_normalizer-3.4.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_charset_normalizer_sdist_223217c3\":[{\"filename\":\"charset_normalizer-3.4.0.tar.gz\",\"version\":\"3.11\"}]}", "cryptography": "{\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_aarch64_846da004\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_17_x86_64_0f996e72\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_aarch64_f7b178f1\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_28_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_manylinux_2_28_x86_64_c2e6fc39\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-manylinux_2_28_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_aarch64_e1be4655\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_cp39_abi3_musllinux_1_2_x86_64_df6b6c6d\":[{\"filename\":\"cryptography-43.0.3-cp39-abi3-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_cryptography_sdist_315b9001\":[{\"filename\":\"cryptography-43.0.3.tar.gz\",\"version\":\"3.11\"}]}", "docutils": "{\"rules_python_publish_deps_311_docutils_py3_none_any_dafca5b9\":[{\"filename\":\"docutils-0.21.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_docutils_sdist_3a6b1873\":[{\"filename\":\"docutils-0.21.2.tar.gz\",\"version\":\"3.11\"}]}", "idna": "{\"rules_python_publish_deps_311_idna_py3_none_any_946d195a\":[{\"filename\":\"idna-3.10-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_idna_sdist_12f65c9b\":[{\"filename\":\"idna-3.10.tar.gz\",\"version\":\"3.11\"}]}", "importlib_metadata": "{\"rules_python_publish_deps_311_importlib_metadata_py3_none_any_45e54197\":[{\"filename\":\"importlib_metadata-8.5.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_importlib_metadata_sdist_71522656\":[{\"filename\":\"importlib_metadata-8.5.0.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_classes": "{\"rules_python_publish_deps_311_jaraco_classes_py3_none_any_f662826b\":[{\"filename\":\"jaraco.classes-3.4.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_classes_sdist_47a024b5\":[{\"filename\":\"jaraco.classes-3.4.0.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_context": "{\"rules_python_publish_deps_311_jaraco_context_py3_none_any_f797fc48\":[{\"filename\":\"jaraco.context-6.0.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_context_sdist_9bae4ea5\":[{\"filename\":\"jaraco_context-6.0.1.tar.gz\",\"version\":\"3.11\"}]}", "jaraco_functools": "{\"rules_python_publish_deps_311_jaraco_functools_py3_none_any_ad159f13\":[{\"filename\":\"jaraco.functools-4.1.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jaraco_functools_sdist_70f7e0e2\":[{\"filename\":\"jaraco_functools-4.1.0.tar.gz\",\"version\":\"3.11\"}]}", "jeepney": "{\"rules_python_publish_deps_311_jeepney_py3_none_any_c0a454ad\":[{\"filename\":\"jeepney-0.8.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_jeepney_sdist_5efe48d2\":[{\"filename\":\"jeepney-0.8.0.tar.gz\",\"version\":\"3.11\"}]}", "keyring": "{\"rules_python_publish_deps_311_keyring_py3_none_any_5426f817\":[{\"filename\":\"keyring-25.4.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_keyring_sdist_b07ebc55\":[{\"filename\":\"keyring-25.4.1.tar.gz\",\"version\":\"3.11\"}]}", "markdown_it_py": "{\"rules_python_publish_deps_311_markdown_it_py_py3_none_any_35521684\":[{\"filename\":\"markdown_it_py-3.0.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_markdown_it_py_sdist_e3f60a94\":[{\"filename\":\"markdown-it-py-3.0.0.tar.gz\",\"version\":\"3.11\"}]}", "mdurl": "{\"rules_python_publish_deps_311_mdurl_py3_none_any_84008a41\":[{\"filename\":\"mdurl-0.1.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_mdurl_sdist_bb413d29\":[{\"filename\":\"mdurl-0.1.2.tar.gz\",\"version\":\"3.11\"}]}", "more_itertools": "{\"rules_python_publish_deps_311_more_itertools_py3_none_any_037b0d32\":[{\"filename\":\"more_itertools-10.5.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_more_itertools_sdist_5482bfef\":[{\"filename\":\"more-itertools-10.5.0.tar.gz\",\"version\":\"3.11\"}]}", "nh3": "{\"rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_14c5a72e\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_macosx_10_12_x86_64_7b7c2a3c\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-macosx_10_12_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_aarch64_42c64511\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_armv7l_0411beb0\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64_5f36b271\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_ppc64le_34c03fa7\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_s390x_19aaba96\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_manylinux_2_17_x86_64_de3ceed6\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_aarch64_f0eca9ca\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_aarch64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_armv7l_3a157ab1\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_armv7l.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_musllinux_1_2_x86_64_36c95d4b\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-musllinux_1_2_x86_64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_cp37_abi3_win_amd64_8ce0f819\":[{\"filename\":\"nh3-0.2.18-cp37-abi3-win_amd64.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_nh3_sdist_94a16692\":[{\"filename\":\"nh3-0.2.18.tar.gz\",\"version\":\"3.11\"}]}", "pkginfo": "{\"rules_python_publish_deps_311_pkginfo_py3_none_any_889a6da2\":[{\"filename\":\"pkginfo-1.10.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pkginfo_sdist_5df73835\":[{\"filename\":\"pkginfo-1.10.0.tar.gz\",\"version\":\"3.11\"}]}", "pycparser": "{\"rules_python_publish_deps_311_pycparser_py3_none_any_c3702b6d\":[{\"filename\":\"pycparser-2.22-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pycparser_sdist_491c8be9\":[{\"filename\":\"pycparser-2.22.tar.gz\",\"version\":\"3.11\"}]}", "pygments": "{\"rules_python_publish_deps_311_pygments_py3_none_any_b8e6aca0\":[{\"filename\":\"pygments-2.18.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pygments_sdist_786ff802\":[{\"filename\":\"pygments-2.18.0.tar.gz\",\"version\":\"3.11\"}]}", "pywin32_ctypes": "{\"rules_python_publish_deps_311_pywin32_ctypes_py3_none_any_8a151337\":[{\"filename\":\"pywin32_ctypes-0.2.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_pywin32_ctypes_sdist_d162dc04\":[{\"filename\":\"pywin32-ctypes-0.2.3.tar.gz\",\"version\":\"3.11\"}]}", "readme_renderer": "{\"rules_python_publish_deps_311_readme_renderer_py3_none_any_2fbca89b\":[{\"filename\":\"readme_renderer-44.0-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_readme_renderer_sdist_8712034e\":[{\"filename\":\"readme_renderer-44.0.tar.gz\",\"version\":\"3.11\"}]}", "requests": "{\"rules_python_publish_deps_311_requests_py3_none_any_70761cfe\":[{\"filename\":\"requests-2.32.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_requests_sdist_55365417\":[{\"filename\":\"requests-2.32.3.tar.gz\",\"version\":\"3.11\"}]}", "requests_toolbelt": "{\"rules_python_publish_deps_311_requests_toolbelt_py2_none_any_cccfdd66\":[{\"filename\":\"requests_toolbelt-1.0.0-py2.py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_requests_toolbelt_sdist_7681a0a3\":[{\"filename\":\"requests-toolbelt-1.0.0.tar.gz\",\"version\":\"3.11\"}]}", "rfc3986": "{\"rules_python_publish_deps_311_rfc3986_py2_none_any_50b1502b\":[{\"filename\":\"rfc3986-2.0.0-py2.py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_rfc3986_sdist_97aacf9d\":[{\"filename\":\"rfc3986-2.0.0.tar.gz\",\"version\":\"3.11\"}]}", "rich": "{\"rules_python_publish_deps_311_rich_py3_none_any_9836f509\":[{\"filename\":\"rich-13.9.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_rich_sdist_bc1e01b8\":[{\"filename\":\"rich-13.9.3.tar.gz\",\"version\":\"3.11\"}]}", "secretstorage": "{\"rules_python_publish_deps_311_secretstorage_py3_none_any_f356e662\":[{\"filename\":\"SecretStorage-3.3.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_secretstorage_sdist_2403533e\":[{\"filename\":\"SecretStorage-3.3.3.tar.gz\",\"version\":\"3.11\"}]}", "twine": "{\"rules_python_publish_deps_311_twine_py3_none_any_215dbe7b\":[{\"filename\":\"twine-5.1.1-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_twine_sdist_9aa08251\":[{\"filename\":\"twine-5.1.1.tar.gz\",\"version\":\"3.11\"}]}", "urllib3": "{\"rules_python_publish_deps_311_urllib3_py3_none_any_ca899ca0\":[{\"filename\":\"urllib3-2.2.3-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_urllib3_sdist_e7d814a8\":[{\"filename\":\"urllib3-2.2.3.tar.gz\",\"version\":\"3.11\"}]}", "zipp": "{\"rules_python_publish_deps_311_zipp_py3_none_any_a817ac80\":[{\"filename\":\"zipp-3.20.2-py3-none-any.whl\",\"version\":\"3.11\"}],\"rules_python_publish_deps_311_zipp_sdist_bc9eb26f\":[{\"filename\":\"zipp-3.20.2.tar.gz\",\"version\":\"3.11\"}]}"}, "packages": ["backports_tarfile", "certifi", "charset_normalizer", "docutils", "idna", "importlib_metadata", "jaraco_classes", "jaraco_context", "jaraco_functools", "keyring", "markdown_it_py", "mdurl", "more_itertools", "nh3", "pkginfo", "pygments", "readme_renderer", "requests", "requests_toolbelt", "rfc3986", "rich", "twine", "urllib3", "zipp"], "groups": {}}}}, "moduleExtensionMetadata": {"useAllRepos": "NO", "reproducible": false}, "recordedRepoMappingEntries": [["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"], ["rules_python+", "bazel_features", "bazel_features+"], ["rules_python+", "bazel_skylib", "bazel_skylib+"], ["rules_python+", "bazel_tools", "bazel_tools"], ["rules_python+", "pypi__build", "rules_python++internal_deps+pypi__build"], ["rules_python+", "pypi__click", "rules_python++internal_deps+pypi__click"], ["rules_python+", "pypi__colorama", "rules_python++internal_deps+pypi__colorama"], ["rules_python+", "pypi__importlib_metadata", "rules_python++internal_deps+pypi__importlib_metadata"], ["rules_python+", "pypi__installer", "rules_python++internal_deps+pypi__installer"], ["rules_python+", "pypi__more_itertools", "rules_python++internal_deps+pypi__more_itertools"], ["rules_python+", "pypi__packaging", "rules_python++internal_deps+pypi__packaging"], ["rules_python+", "pypi__pep517", "rules_python++internal_deps+pypi__pep517"], ["rules_python+", "pypi__pip", "rules_python++internal_deps+pypi__pip"], ["rules_python+", "pypi__pip_tools", "rules_python++internal_deps+pypi__pip_tools"], ["rules_python+", "pypi__pyproject_hooks", "rules_python++internal_deps+pypi__pyproject_hooks"], ["rules_python+", "pypi__setuptools", "rules_python++internal_deps+pypi__setuptools"], ["rules_python+", "pypi__tomli", "rules_python++internal_deps+pypi__tomli"], ["rules_python+", "pypi__wheel", "rules_python++internal_deps+pypi__wheel"], ["rules_python+", "pypi__zipp", "rules_python++internal_deps+pypi__zipp"], ["rules_python+", "pythons_hub", "rules_python++python+pythons_hub"], ["rules_python++python+pythons_hub", "python_3_10_host", "rules_python++python+python_3_10_host"], ["rules_python++python+pythons_hub", "python_3_11_host", "rules_python++python+python_3_11_host"], ["rules_python++python+pythons_hub", "python_3_12_host", "rules_python++python+python_3_12_host"], ["rules_python++python+pythons_hub", "python_3_13_host", "rules_python++python+python_3_13_host"], ["rules_python++python+pythons_hub", "python_3_8_host", "rules_python++python+python_3_8_host"], ["rules_python++python+pythons_hub", "python_3_9_host", "rules_python++python+python_3_9_host"]]}}, "@@rules_swift+//swift:extensions.bzl%non_module_deps": {"general": {"bzlTransitiveDigest": "188HAR1B7/zgGCexknNy/LA9sxfIShSv1ttw1bk62XM=", "usagesDigest": "mhACFnrdMv9Wi0Mt67bxocJqviRkDSV+Ee5Mqdj5akA=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_github_apple_swift_protobuf": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-protobuf/archive/1.20.2.tar.gz"], "sha256": "3fb50bd4d293337f202d917b6ada22f9548a0a0aed9d9a4d791e6fbd8a246ebb", "strip_prefix": "swift-protobuf-1.20.2/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_protobuf/BUILD.overlay"}}, "com_github_grpc_grpc_swift": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/grpc/grpc-swift/archive/1.16.0.tar.gz"], "sha256": "58b60431d0064969f9679411264b82e40a217ae6bd34e17096d92cc4e47556a5", "strip_prefix": "grpc-swift-1.16.0/", "build_file": "@@rules_swift+//third_party:com_github_grpc_grpc_swift/BUILD.overlay"}}, "com_github_apple_swift_docc_symbolkit": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-docc-symbolkit/archive/refs/tags/swift-5.10-RELEASE.tar.gz"], "sha256": "de1d4b6940468ddb53b89df7aa1a81323b9712775b0e33e8254fa0f6f7469a97", "strip_prefix": "swift-docc-symbolkit-swift-5.10-RELEASE", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_docc_symbolkit/BUILD.overlay"}}, "com_github_apple_swift_nio": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-nio/archive/2.42.0.tar.gz"], "sha256": "e3304bc3fb53aea74a3e54bd005ede11f6dc357117d9b1db642d03aea87194a0", "strip_prefix": "swift-nio-2.42.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_nio/BUILD.overlay"}}, "com_github_apple_swift_nio_http2": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-nio-http2/archive/1.26.0.tar.gz"], "sha256": "f0edfc9d6a7be1d587e5b403f2d04264bdfae59aac1d74f7d974a9022c6d2b25", "strip_prefix": "swift-nio-http2-1.26.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_nio_http2/BUILD.overlay"}}, "com_github_apple_swift_nio_transport_services": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-nio-transport-services/archive/1.15.0.tar.gz"], "sha256": "f3498dafa633751a52b9b7f741f7ac30c42bcbeb3b9edca6d447e0da8e693262", "strip_prefix": "swift-nio-transport-services-1.15.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_nio_transport_services/BUILD.overlay"}}, "com_github_apple_swift_nio_extras": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-nio-extras/archive/1.4.0.tar.gz"], "sha256": "4684b52951d9d9937bb3e8ccd6b5daedd777021ef2519ea2f18c4c922843b52b", "strip_prefix": "swift-nio-extras-1.4.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_nio_extras/BUILD.overlay"}}, "com_github_apple_swift_log": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-log/archive/1.4.4.tar.gz"], "sha256": "48fe66426c784c0c20031f15dc17faf9f4c9037c192bfac2f643f65cb2321ba0", "strip_prefix": "swift-log-1.4.4/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_log/BUILD.overlay"}}, "com_github_apple_swift_nio_ssl": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-nio-ssl/archive/2.23.0.tar.gz"], "sha256": "4787c63f61dd04d99e498adc3d1a628193387e41efddf8de19b8db04544d016d", "strip_prefix": "swift-nio-ssl-2.23.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_nio_ssl/BUILD.overlay"}}, "com_github_apple_swift_collections": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-collections/archive/1.0.4.tar.gz"], "sha256": "d9e4c8a91c60fb9c92a04caccbb10ded42f4cb47b26a212bc6b39cc390a4b096", "strip_prefix": "swift-collections-1.0.4/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_collections/BUILD.overlay"}}, "com_github_apple_swift_atomics": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/apple/swift-atomics/archive/1.1.0.tar.gz"], "sha256": "1bee7f469f7e8dc49f11cfa4da07182fbc79eab000ec2c17bfdce468c5d276fb", "strip_prefix": "swift-atomics-1.1.0/", "build_file": "@@rules_swift+//third_party:com_github_apple_swift_atomics/BUILD.overlay"}}, "build_bazel_rules_swift_index_import": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file": "@@rules_swift+//third_party:build_bazel_rules_swift_index_import/BUILD.overlay", "canonical_id": "index-import-5.8", "urls": ["https://github.com/MobileNativeFoundation/index-import/releases/download/*******/index-import.tar.gz"], "sha256": "28c1ffa39d99e74ed70623899b207b41f79214c498c603915aef55972a851a15"}}, "build_bazel_rules_swift_local_config": {"repoRuleId": "@@rules_swift+//swift/internal:swift_autoconfiguration.bzl%swift_autoconfiguration", "attributes": {}}}, "recordedRepoMappingEntries": [["rules_swift+", "bazel_tools", "bazel_tools"], ["rules_swift+", "build_bazel_rules_swift", "rules_swift+"]]}}}}